import { BaseModel, column, hasMany } from '@adonisjs/lucid/orm'
import ColumnData from '#models/core/types/column_data'
import Configuration from '#models/core/configuration'
import type { HasMany } from '@adonisjs/lucid/types/relations'

export default class ConfigurationGroup extends BaseModel {
    public static table = 'configuration_groups'
    public static uniques = ['code']
    public static columns: Record<string, ColumnData> = {
        id: {
            type: 'int',
            length: 11,
            required: true,
        },
        name: {
            type: 'varchar',
            length: 50,
            required: true,
        },
        code: {
            type: 'varchar',
            length: 50,
            required: false,
        },
        icon: {
            type: 'varchar',
            length: 100,
            required: false,
        },
        order: {
            type: 'int',
            length: 11,
            required: false,
        },
        is_privated: {
            type: 'int',
            length: 11,
            required: false,
        },
    }

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare name: string

    @column()
    declare code: string

    @column()
    declare icon: string

    @column()
    declare order: number

    @column()
    declare is_privated: number

    @hasMany(() => Configuration, {
        localKey: 'id',
        foreignKey: 'group_id',
    })
    declare configuration: HasMany<typeof Configuration>
}
