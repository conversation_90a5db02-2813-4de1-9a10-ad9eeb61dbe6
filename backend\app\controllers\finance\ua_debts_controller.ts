// import type { HttpContext } from '@adonisjs/core/http'

import { Get, ApiOnly, Prefix } from '#libraries/vcms/route_decorators'
import { HttpContext } from '@adonisjs/core/http'
import Database from '@adonisjs/lucid/services/db'
import FinanceCurrenciesMonth from '#models/finance/finance_currencies_month'

@ApiOnly()
@Prefix('/api/ua_debts')
export default class UaDebtsController {
    /**
     * Lấy tổng quan công nợ UA
     */
    @Get('/overview')
    public async getUaDebtsOverview({ request, response }: HttpContext) {
        try {
            const {
                timeFrame = 'month',
                period = 'current',
                selectedMonth,
                selectedYear,
                selectedQuarter
            } = request.qs()
            const currentTime = Math.floor(Date.now() / 1000)

            // Tính toán khoảng thời gian hiện tại và kỳ trước
            const { currentPeriod, previousPeriod } = this.calculatePeriods(
                timeFrame,
                period,
                selectedMonth,
                selectedYear,
                selectedQuarter
            )

            // Tổng công nợ chờ thanh toán (status = 0 - Pending)
            const pendingDebtsQuery = Database.from('finance_ua_debts')
                .select(['value', 'currency', 'month', 'year'])
                .where('status', -1)

            // Thêm điều kiện lọc theo thời gian
            this.addPeriodFilter(pendingDebtsQuery, currentPeriod, timeFrame)

            const pendingDebtsResult = await pendingDebtsQuery
            const totalPendingDebt = await this.convertToUSD(pendingDebtsResult)

            // Tổng công nợ quá hạn (status = 0 và due_date < hiện tại) - từ đầu đến giờ
            const overdueDebtsQuery = Database.from('finance_ua_debts')
                .select(['value', 'currency', 'month', 'year'])
                .where('status', -1)
                .where('due_date', '<', currentTime)

            const overdueDebtsResult = await overdueDebtsQuery
            const totalOverdueDebt = await this.convertToUSD(overdueDebtsResult)

            // Tổng chi phí UA kỳ hiện tại
            const currentCostQuery = Database.from('finance_ua_debts')
                .select(['value', 'currency', 'month', 'year'])

            this.addPeriodFilter(currentCostQuery, currentPeriod, timeFrame)

            const currentCostResult = await currentCostQuery
            const currentPeriodCost = await this.convertToUSD(currentCostResult)

            // Tổng chi phí UA kỳ trước để so sánh
            const previousCostQuery = Database.from('finance_ua_debts')
                .select(['value', 'currency', 'month', 'year'])

            this.addPeriodFilter(previousCostQuery, previousPeriod, timeFrame)

            const previousCostResult = await previousCostQuery
            const previousPeriodCost = await this.convertToUSD(previousCostResult)

            return response.json({
                success: true,
                data: {
                    totalPendingDebt: Math.round(totalPendingDebt * 100) / 100,
                    totalOverdueDebt: Math.round(totalOverdueDebt * 100) / 100,
                    currentPeriodCost: Math.round(currentPeriodCost * 100) / 100,
                    previousPeriodCost: Math.round(previousPeriodCost * 100) / 100,
                    changeRate: previousPeriodCost > 0 ?
                        ((currentPeriodCost - previousPeriodCost) / previousPeriodCost * 100).toFixed(2) : 0,
                    timeFrame,
                    period,
                    currentPeriod,
                    previousPeriod
                }
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy tổng quan công nợ UA',
                error: error.message
            })
        }
    }

    /**
     * Lấy phân bổ chi phí theo network
     */
    @Get('/network-distribution')
    public async getNetworkDistribution({ request, response }: HttpContext) {
        try {
            const {
                timeFrame = 'month',
                period = 'current',
                selectedMonth,
                selectedYear,
                selectedQuarter
            } = request.qs()
            const { currentPeriod } = this.calculatePeriods(
                timeFrame,
                period,
                selectedMonth,
                selectedYear,
                selectedQuarter
            )

            const query = Database.from('finance_ua_debts')
                .select(['network', 'value', 'currency', 'month', 'year'])

            this.addPeriodFilter(query, currentPeriod, timeFrame)

            const rawData = await query

            // Group by network và convert về USD
            const networkGroups: Record<string, any[]> = {}
            rawData.forEach(item => {
                const networkName = item.network || 'Unknown'
                if (!networkGroups[networkName]) {
                    networkGroups[networkName] = []
                }
                networkGroups[networkName].push(item)
            })

            // Convert từng group về USD
            const networkData = []
            for (const [networkName, items] of Object.entries(networkGroups)) {
                const totalUSD = await this.convertToUSD(items)
                networkData.push({
                    name: networkName,
                    value: totalUSD
                })
            }

            // Sort by value desc
            networkData.sort((a, b) => b.value - a.value)

            return response.json({
                success: true,
                data: networkData
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy phân bổ network',
                error: error.message
            })
        }
    }

    /**
     * Lấy chi phí UA theo tháng
     */
    @Get('/monthly-cost')
    public async getMonthlyCost({ request, response }: HttpContext) {
        try {
            const { year = new Date().getFullYear() } = request.qs()

            const rawData = await Database.from('finance_ua_debts')
                .select(['month', 'value', 'currency', 'year'])
                .where('year', year)
                .orderBy('month', 'asc')

            // Group by month và convert về USD
            const monthGroups: Record<number, any[]> = {}
            rawData.forEach(item => {
                if (!monthGroups[item.month]) {
                    monthGroups[item.month] = []
                }
                monthGroups[item.month].push(item)
            })

            // Tạo dữ liệu cho 12 tháng
            const result = []
            for (let i = 1; i <= 12; i++) {
                const monthItems = monthGroups[i] || []
                const costUSD = await this.convertToUSD(monthItems)
                result.push({
                    month: `T${i}`,
                    cost: costUSD
                })
            }

            return response.json({
                success: true,
                data: result
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy chi phí UA theo tháng',
                error: error.message
            })
        }
    }

    /**
     * Lấy danh sách chi tiết công nợ UA
     */
    @Get('/debts')
    public async getDebts({ request, response }: HttpContext) {
        try {
            const {
                timeFrame = 'month',
                period = 'current',
                page = 1,
                limit = 200,
                networkFilter,
                statusFilter,
                selectedMonth,
                selectedYear,
                selectedQuarter
            } = request.qs()

            const { currentPeriod } = this.calculatePeriods(
                timeFrame,
                period,
                selectedMonth,
                selectedYear,
                selectedQuarter
            )
            const currentTime = Math.floor(Date.now() / 1000)

            const query = Database.from('finance_ua_debts')
                .select('*')
                .orderBy('due_date', 'desc')

            this.addPeriodFilter(query, currentPeriod, timeFrame)

            // Thêm filter theo Network
            if (networkFilter && networkFilter !== 'all') {
                query.where('network', networkFilter)
            }

            // Thêm filter theo Status
            if (statusFilter && statusFilter !== 'all') {
                query.where('status', parseInt(statusFilter))
            }

            const debts = await query.paginate(page, limit)

            // Format dữ liệu cho frontend
            const formattedData = debts.all().map(debt => ({
                id: debt.id,
                network: debt.network,
                networkCode: debt.network_code,
                month: `${debt.month.toString().padStart(2, '0')}/${debt.year}`,
                cost: Math.round(debt.value * 100) / 100,
                currency: debt.currency || 'USD',
                costDisplay: `${(Math.round(debt.value * 100) / 100).toLocaleString()} ${debt.currency || 'USD'}`,
                dueDate: this.formatTimestampToDate(debt.due_date),
                paymentDate: debt.payment_date ? this.formatTimestampToDate(debt.payment_date) : null,
                status: this.getStatusText(debt.status),
                statusCode: debt.status,
                overdue: debt.status === 0 && debt.due_date < currentTime,
                feeBank: Math.round(debt.fee_bank * 100) / 100,
                paymentNumber: debt.payment_number,
                paymentName: debt.payment_name,
                note: debt.note,
                contentTransfer: debt.content_transfer,
                swiftCode: debt.swift_code
            }))

            return response.json({
                success: true,
                data: formattedData,
                meta: {
                    total: debts.total,
                    perPage: debts.perPage,
                    currentPage: debts.currentPage,
                    lastPage: debts.lastPage
                }
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy danh sách công nợ UA',
                error: error.message
            })
        }
    }

    /**
     * Lấy thống kê theo kỳ so sánh
     */
    @Get('/comparison')
    public async getComparison({ request, response }: HttpContext) {
        try {
            const { timeFrame = 'month' } = request.qs()

            const currentDate = new Date()
            const currentYear = currentDate.getFullYear()
            const currentMonth = currentDate.getMonth() + 1
            const currentQuarter = Math.ceil(currentMonth / 3)

            let currentPeriod, previousPeriod

            if (timeFrame === 'month') {
                currentPeriod = { month: currentMonth, year: currentYear }
                previousPeriod = currentMonth > 1
                    ? { month: currentMonth - 1, year: currentYear }
                    : { month: 12, year: currentYear - 1 }
            } else if (timeFrame === 'quarter') {
                currentPeriod = { quarter: currentQuarter, year: currentYear }
                previousPeriod = currentQuarter > 1
                    ? { quarter: currentQuarter - 1, year: currentYear }
                    : { quarter: 4, year: currentYear - 1 }
            } else {
                currentPeriod = { year: currentYear }
                previousPeriod = { year: currentYear - 1 }
            }

            // Thống kê kỳ hiện tại
            const currentStats = await this.getPeriodStats(currentPeriod, timeFrame)
            const previousStats = await this.getPeriodStats(previousPeriod, timeFrame)

            return response.json({
                success: true,
                data: {
                    current: currentStats,
                    previous: previousStats,
                    comparison: {
                        costChange: previousStats.totalCost > 0
                            ? ((currentStats.totalCost - previousStats.totalCost) / previousStats.totalCost * 100).toFixed(2)
                            : 0,
                        pendingChange: previousStats.pendingDebt > 0
                            ? ((currentStats.pendingDebt - previousStats.pendingDebt) / previousStats.pendingDebt * 100).toFixed(2)
                            : 0
                    }
                }
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy thống kê so sánh',
                error: error.message
            })
        }
    }

    /**
     * Lấy danh sách filter options (Networks và Statuses)
     */
    @Get('/filter-options')
    public async getFilterOptions({ response }: HttpContext) {
        try {
            // Lấy danh sách Networks
            const networks = await Database.from('finance_ua_debts')
                .distinct('network')
                .whereNotNull('network')
                .orderBy('network', 'asc')

            // Lấy danh sách Statuses
            const statuses = [
                { value: '-1', label: 'Stop' },
                { value: '0', label: 'Pending' },
                { value: '1', label: 'Done' },
                { value: '2', label: 'Prepaid' },
                { value: '3', label: 'Auto charge' }
            ]

            return response.json({
                success: true,
                data: {
                    networks: networks.map(item => ({
                        value: item.network,
                        label: item.network
                    })),
                    statuses
                }
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy filter options',
                error: error.message
            })
        }
    }

    // Helper methods
    private calculatePeriods(
        timeFrame: string,
        period: string,
        selectedMonth?: string,
        selectedYear?: string,
        selectedQuarter?: string
    ) {
        const currentDate = new Date()
        const currentYear = currentDate.getFullYear()
        const currentMonth = currentDate.getMonth() + 1
        const currentQuarter = Math.ceil(currentMonth / 3)

        // Sử dụng giá trị được chọn hoặc mặc định theo thời gian hiện tại
        const year = selectedYear ? parseInt(selectedYear) : currentYear
        const month = selectedMonth ? parseInt(selectedMonth) : currentMonth
        const quarter = selectedQuarter ? parseInt(selectedQuarter) : currentQuarter

        let currentPeriod, previousPeriod

        if (timeFrame === 'month') {
            if (period === 'current') {
                currentPeriod = { month, year }
                previousPeriod = month > 1
                    ? { month: month - 1, year }
                    : { month: 12, year: year - 1 }
            } else {
                // Kỳ trước
                const prevMonth = month > 1 ? month - 1 : 12
                const prevYear = month > 1 ? year : year - 1
                currentPeriod = { month: prevMonth, year: prevYear }
                previousPeriod = prevMonth > 1
                    ? { month: prevMonth - 1, year: prevYear }
                    : { month: 12, year: prevYear - 1 }
            }
        } else if (timeFrame === 'quarter') {
            if (period === 'current') {
                currentPeriod = { quarter, year }
                previousPeriod = quarter > 1
                    ? { quarter: quarter - 1, year }
                    : { quarter: 4, year: year - 1 }
            } else {
                // Kỳ trước
                const prevQuarter = quarter > 1 ? quarter - 1 : 4
                const prevYear = quarter > 1 ? year : year - 1
                currentPeriod = { quarter: prevQuarter, year: prevYear }
                previousPeriod = prevQuarter > 1
                    ? { quarter: prevQuarter - 1, year: prevYear }
                    : { quarter: 4, year: prevYear - 1 }
            }
        } else {
            if (period === 'current') {
                currentPeriod = { year }
                previousPeriod = { year: year - 1 }
            } else {
                currentPeriod = { year: year - 1 }
                previousPeriod = { year: year - 2 }
            }
        }

        return { currentPeriod, previousPeriod }
    }

    private addPeriodFilter(query: any, period: any, timeFrame: string, useAlias: boolean = false) {
        const prefix = useAlias ? 'fmd.' : ''

        if (timeFrame === 'month') {
            query.where(`${prefix}month`, period.month).where(`${prefix}year`, period.year)
        } else if (timeFrame === 'quarter') {
            query.where(`${prefix}quarter`, period.quarter).where(`${prefix}year`, period.year)
        } else {
            query.where(`${prefix}year`, period.year)
        }
    }

    private async getPeriodStats(period: any, timeFrame: string) {
        const query = Database.from('finance_ua_debts')
            .select(['value', 'currency', 'month', 'year', 'status'])
        this.addPeriodFilter(query, period, timeFrame)

        const allData = await query

        // Tính tổng chi phí (convert về USD)
        const totalCost = await this.convertToUSD(allData)

        // Tính công nợ chờ thanh toán (convert về USD)
        const pendingData = allData.filter(item => item.status === -1)
        const pendingDebt = await this.convertToUSD(pendingData)

        // Thống kê records
        const totalRecords = allData.length
        const paidRecords = allData.filter(item => item.status === 1).length

        return {
            totalCost,
            pendingDebt,
            totalRecords,
            paidRecords,
            paymentRate: totalRecords > 0
                ? (paidRecords / totalRecords * 100).toFixed(2)
                : 0
        }
    }

    private getStatusText(status: number): string {
        const statusMap: Record<number, string> = {
            '-1': 'Stop',
            0: 'Pending',
            1: 'Done',
            2: 'Prepaid',
            3: 'Auto charge'
        }
        return statusMap[status] || 'Unknown'
    }

    private formatTimestampToDate(timestamp: number): string {
        if (!timestamp) return ''
        return new Date(timestamp * 1000).toISOString().split('T')[0]
    }

    /**
     * Chuyển đổi danh sách amounts về USD dựa trên tỷ giá
     */
    private async convertToUSD(debts: any[]): Promise<number> {
        let totalUSD = 0

        for (const debt of debts) {
            if (debt.currency === 'USD') {
                totalUSD += parseFloat(debt.value) || 0
            } else {
                // Tìm tỷ giá cho currency và tháng cụ thể
                const monthKey = `${debt.year}-${debt.month.toString().padStart(2, '0')}`
                const rate = await FinanceCurrenciesMonth.query()
                    .where('name', debt.currency)
                    .where('month', monthKey)
                    .first()

                if (rate && rate.rate > 0) {
                    totalUSD += (parseFloat(debt.value) || 0) / rate.rate
                } else {
                    // Fallback rate nếu không tìm thấy
                    const fallbackRates: Record<string, number> = {
                        'VND': 24000,
                        'COP': 4000,
                        'EUR': 0.85,
                        'GBP': 0.75
                    }
                    const fallbackRate = fallbackRates[debt.currency] || 1
                    totalUSD += (parseFloat(debt.value) || 0) / fallbackRate
                }
            }
        }

        return Math.round(totalUSD * 100) / 100
    }
}