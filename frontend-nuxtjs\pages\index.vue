<template>
    <div style="background: #f5f5f5; min-height: 100vh">
        <!-- Header with Controls -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px">
            <h1 style="margin: 0; font-size: 24px; font-weight: 600; color: #2196f3; text-decoration: underline; text-underline-offset: 4px; text-decoration-thickness: 3px">Tổng quan tài chính</h1>

            <div style="display: flex; gap: 12px; align-items: center; padding: 16px; background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06)">
                <div style="position: relative">
                    <label style="position: absolute; top: -8px; left: 12px; background: white; padding: 0 6px; font-size: 12px; color: rgba(0, 0, 0, 0.6); z-index: 1"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>ian</label>
                    <a-select v-model:value="timeFrame" style="width: 120px" @change="handleTimeFrameChange" :bordered="true">
                        <a-select-option value="month">Tháng</a-select-option>
                        <a-select-option value="quarter">Quý</a-select-option>
                        <a-select-option value="year">Năm</a-select-option>
                    </a-select>
                </div>

                <div v-if="timeFrame === 'month'" style="position: relative">
                    <label style="position: absolute; top: -8px; left: 12px; background: white; padding: 0 6px; font-size: 12px; color: rgba(0, 0, 0, 0.6); z-index: 1">Tháng</label>
                    <a-select v-model:value="selectedMonth" style="width: 120px" @change="handleMonthChange" :bordered="true">
                        <a-select-option v-for="month in availableMonths" :key="month.value" :value="month.value">{{ month.label }}</a-select-option>
                    </a-select>
                </div>

                <div v-if="timeFrame === 'quarter'" style="position: relative">
                    <label style="position: absolute; top: -8px; left: 12px; background: white; padding: 0 6px; font-size: 12px; color: rgba(0, 0, 0, 0.6); z-index: 1">Quý</label>
                    <a-select v-model:value="selectedQuarter" style="width: 120px" @change="handleQuarterChange" :bordered="true">
                        <a-select-option v-for="quarter in availableQuarters" :key="quarter.value" :value="quarter.value">{{ quarter.label }}</a-select-option>
                    </a-select>
                </div>

                <div style="position: relative">
                    <label style="position: absolute; top: -8px; left: 12px; background: white; padding: 0 6px; font-size: 12px; color: rgba(0, 0, 0, 0.6); z-index: 1">Năm</label>
                    <a-select v-model:value="selectedYear" style="width: 120px" @change="handleYearChange" :bordered="true">
                        <a-select-option v-for="year in availableYears" :key="year" :value="year.toString()">{{ year }}</a-select-option>
                    </a-select>
                </div>

                <div style="position: relative">
                    <label style="position: absolute; top: -8px; left: 12px; background: white; padding: 0 6px; font-size: 12px; color: rgba(0, 0, 0, 0.6); z-index: 1">So sánh</label>
                    <a-select v-model:value="period" style="width: 120px" @change="handlePeriodChange" :bordered="true">
                        <a-select-option value="current">Hiện tại</a-select-option>
                        <a-select-option value="previous">Kỳ trước</a-select-option>
                    </a-select>
                </div>

                <div style="position: relative">
                    <label style="position: absolute; top: -8px; left: 12px; background: white; padding: 0 6px; font-size: 12px; color: rgba(0, 0, 0, 0.6); z-index: 1">Loại sản phẩm</label>
                    <a-select v-model:value="productType" style="width: 120px" @change="handleProductTypeChange" :bordered="true">
                        <a-select-option value="all">Tất cả</a-select-option>
                        <a-select-option value="game">Game</a-select-option>
                        <a-select-option value="app">App</a-select-option>
                    </a-select>
                </div>
            </div>
        </div>

        <!-- Financial Overview Section -->
        <a-card :bordered="true" style="margin-bottom: 24px; background: white; border: 1px solid #e8e8e8; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border-top: 4px solid #2196f3; border-radius: 8px">
            <template #title>
                <div style="display: flex; align-items: center">
                    <BankOutlined style="margin-right: 8px; color: #2196f3; font-size: 18px" />
                    <span style="color: #2196f3; font-weight: bold; font-size: 18px">Tổng quan tài chính - {{ getTimeFrameLabel() }}</span>
                </div>
            </template>

            <!-- Loading state -->
            <div v-if="financialOverviewLoading" style="text-align: center; padding: 40px">
                <a-spin />
                <p style="margin-top: 8px; color: #666; font-size: 12px">Đang tải dữ liệu tổng quan tài chính...</p>
            </div>

            <!-- Financial Overview Cards -->
            <a-row v-else :gutter="[24, 24]">
                <a-col :span="8">
                    <div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%); border-radius: 12px; padding: 20px; height: 140px; box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2)">
                        <div style="display: flex; align-items: center; margin-bottom: 8px">
                            <DollarOutlined style="color: #2196f3; font-size: 16px; margin-right: 8px" />
                            <span style="color: #2196f3; font-size: 14px; font-weight: 500">Tổng doanh thu</span>
                        </div>
                        <div style="font-size: 28px; font-weight: bold; color: #2196f3; margin-bottom: 8px; line-height: 1">
                            {{ formatCurrency(financialOverview.totalRevenue) }}
                        </div>
                        <div style="display: flex; margin-top: 12px">
                            <div style="display: inline-flex; align-items: center; background: rgba(255, 255, 255, 0.8); padding: 4px 8px; border-radius: 4px; width: fit-content">
                                <ArrowUpOutlined v-if="financialOverview.revenueChange >= 0" style="color: #4caf50; font-size: 12px; margin-right: 4px" />
                                <ArrowDownOutlined v-else style="color: #f44336; font-size: 12px; margin-right: 4px" />
                                <span :style="`color: ${financialOverview.revenueChange >= 0 ? '#4caf50' : '#f44336'}; font-size: 12px; font-weight: 500`"> {{ financialOverview.revenueChange >= 0 ? "+" : "" }}{{ financialOverview.revenueChange }}% so với kỳ trước </span>
                            </div>
                        </div>
                    </div>
                </a-col>

                <a-col :span="8">
                    <div style="background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 50%, #ffe082 100%); border-radius: 12px; padding: 20px; height: 140px; box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2)">
                        <div style="display: flex; align-items: center; margin-bottom: 8px">
                            <ShoppingOutlined style="color: #ff9800; font-size: 16px; margin-right: 8px" />
                            <span style="color: #ff9800; font-size: 14px; font-weight: 500">Tổng chi phí</span>
                        </div>
                        <div style="font-size: 28px; font-weight: bold; color: #ff9800; margin-bottom: 8px; line-height: 1">
                            {{ formatCurrency(financialOverview.totalExpenses) }}
                        </div>
                        <div style="display: flex; margin-top: 12px">
                            <div style="display: inline-flex; align-items: center; background: rgba(255, 255, 255, 0.8); padding: 4px 8px; border-radius: 4px; width: fit-content">
                                <ArrowUpOutlined v-if="financialOverview.expensesChange >= 0" style="color: #4caf50; font-size: 12px; margin-right: 4px" />
                                <ArrowDownOutlined v-else style="color: #f44336; font-size: 12px; margin-right: 4px" />
                                <span :style="`color: ${financialOverview.expensesChange >= 0 ? '#4caf50' : '#f44336'}; font-size: 12px; font-weight: 500`"> {{ financialOverview.expensesChange >= 0 ? "+" : "" }}{{ financialOverview.expensesChange }}% so với kỳ trước </span>
                            </div>
                        </div>
                    </div>
                </a-col>

                <a-col :span="8">
                    <div style="background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 50%, #a5d6a7 100%); border-radius: 12px; padding: 20px; height: 140px; box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2)">
                        <div style="display: flex; align-items: center; margin-bottom: 8px">
                            <RiseOutlined style="color: #4caf50; font-size: 16px; margin-right: 8px" />
                            <span style="color: #4caf50; font-size: 14px; font-weight: 500">Lợi nhuận ròng</span>
                        </div>
                        <div style="font-size: 28px; font-weight: bold; color: #4caf50; margin-bottom: 8px; line-height: 1">
                            {{ formatCurrency(financialOverview.totalProfit) }}
                        </div>
                        <div style="display: flex; margin-top: 12px">
                            <div style="display: inline-flex; align-items: center; background: rgba(255, 255, 255, 0.8); padding: 4px 8px; border-radius: 4px; width: fit-content">
                                <ArrowUpOutlined v-if="financialOverview.profitChange >= 0" style="color: #4caf50; font-size: 12px; margin-right: 4px" />
                                <ArrowDownOutlined v-else style="color: #f44336; font-size: 12px; margin-right: 4px" />
                                <span :style="`color: ${financialOverview.profitChange >= 0 ? '#4caf50' : '#f44336'}; font-size: 12px; font-weight: 500`"> {{ financialOverview.profitChange >= 0 ? "+" : "" }}{{ financialOverview.profitChange }}% so với kỳ trước </span>
                            </div>
                        </div>
                    </div>
                </a-col>
            </a-row>
        </a-card>

        <!-- Bank Balances and Debt Overview -->
        <a-row :gutter="[24, 24]" style="margin-bottom: 32px">
            <!-- Bank Balances -->
            <a-col :span="12">
                <a-card :bordered="true" style="height: 100%; background: white; border: 1px solid #e8e8e8; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border-top: 4px solid #2196f3; border-radius: 8px">
                    <template #title>
                        <div style="display: flex; justify-content: space-between; align-items: center">
                            <div style="display: flex; align-items: center">
                                <BankOutlined style="margin-right: 8px; color: #2196f3; font-size: 18px" />
                                <span style="color: #2196f3; font-weight: bold; font-size: 18px">Số dư tài khoản ngân hàng</span>
                            </div>
                            <NuxtLink to="/bank-accounts">
                                <a-button type="primary" size="small" style="background: #2196f3; border-color: #2196f3">Chi tiết</a-button>
                            </NuxtLink>
                        </div>
                    </template>

                    <div style="font-size: 32px; color: #2196f3; font-weight: bold; margin-bottom: 24px">
                        <a-spin v-if="bankBalancesLoading" />
                        <span v-else>{{ formatCurrency(bankBalances.totalBalance) }}</span>
                    </div>

                    <!-- Hiển thị động theo dữ liệu từ API -->
                    <div v-if="bankBalancesLoading" style="text-align: center; padding: 20px">
                        <a-spin />
                        <p style="margin-top: 8px; color: #666; font-size: 12px">Đang tải dữ liệu...</p>
                    </div>

                    <a-row v-else :gutter="[16, 16]">
                        <a-col :span="12" v-for="accountType in bankBalances.accountTypes" :key="accountType.account_type">
                            <div style="margin-bottom: 16px">
                                <div style="display: flex; align-items: center; margin-bottom: 4px">
                                    <div :style="`width: 8px; height: 8px; background: ${accountType.color}; border-radius: 50%; margin-right: 6px`"></div>
                                    <span style="color: #666; font-size: 12px">{{ accountType.account_name }}</span>
                                </div>
                                <div style="font-weight: 500; color: #2196f3">{{ formatCurrency(accountType.balance) }}</div>
                            </div>
                        </a-col>
                    </a-row>

                    <!-- Thông tin cập nhật cuối -->
                    <div v-if="bankBalances.last_updated_date && !bankBalancesLoading" style="margin-top: 16px; padding-top: 8px; border-top: 1px solid #f5f5f5">
                        <div style="color: #bbb; font-size: 14px; text-align: center; font-weight: 400">Cập nhật: {{ bankBalances.last_updated_date }}</div>
                    </div>
                </a-card>
            </a-col>

            <!-- Debt Overview -->
            <a-col :span="12">
                <a-card :bordered="true" style="height: 100%; background: white; border: 1px solid #e8e8e8; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border-top: 4px solid #e91e63; border-radius: 8px">
                    <template #title>
                        <div style="display: flex; align-items: center">
                            <ShoppingOutlined style="margin-right: 8px; color: #e91e63; font-size: 18px" />
                            <span style="color: #e91e63; font-weight: bold; font-size: 18px">Chi tiết nguồn chi phí</span>
                        </div>
                    </template>

                    <!-- Loading state -->
                    <div v-if="debtOverviewLoading" style="text-align: center; padding: 40px">
                        <a-spin />
                        <p style="margin-top: 8px; color: #666; font-size: 12px">Đang tải dữ liệu công nợ...</p>
                    </div>

                    <!-- Debt data -->
                    <div v-else style="padding: 4px">
                        <!-- Monetization Debt -->
                        <div style="background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%); border-radius: 10px; padding: 16px; margin-bottom: 12px; box-shadow: 0 2px 6px rgba(255, 77, 79, 0.08)">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px">
                                <div style="display: flex; align-items: center">
                                    <div style="width: 12px; height: 12px; background: #52c41a; border-radius: 50%; margin-right: 8px"></div>
                                    <span style="color: #52c41a; font-weight: 600; font-size: 15px">Công nợ Monetization</span>
                                </div>
                                <NuxtLink to="/monetization_debts">
                                    <a-button size="small" style="background: #e91e63; border-color: #e91e63; color: white; border-radius: 5px; font-weight: 500; font-size: 12px; height: 26px">Chi tiết</a-button>
                                </NuxtLink>
                            </div>

                            <div>
                                <!-- Tổng công nợ -->
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px">
                                    <span style="color: #999; font-size: 13px">Tổng công nợ</span>
                                    <span style="font-weight: 600; color: #eb2f96; font-size: 17px">{{ formatCurrency(debtOverview.totalMonetizationDebt) }}</span>
                                </div>
                                <!-- Công nợ quá hạn -->
                                <div style="display: flex; justify-content: space-between; align-items: center">
                                    <span style="color: #999; font-size: 13px">Công nợ quá hạn</span>
                                    <div style="display: flex; align-items: center">
                                        <WarningOutlined style="color: #ff4d4f; font-size: 14px; margin-right: 5px" />
                                        <span style="font-weight: 600; color: #ff4d4f; font-size: 17px">{{ formatCurrency(debtOverview.overdueMonetizationDebt) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- UA Debt -->
                        <div style="background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%); border-radius: 10px; padding: 16px; box-shadow: 0 2px 6px rgba(255, 77, 79, 0.08)">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px">
                                <div style="display: flex; align-items: center">
                                    <div style="width: 12px; height: 12px; background: #2196f3; border-radius: 50%; margin-right: 8px"></div>
                                    <span style="color: #2196f3; font-weight: 600; font-size: 15px">Công nợ UA</span>
                                </div>
                                <NuxtLink to="/ua_debts">
                                    <a-button size="small" style="background: #e91e63; border-color: #e91e63; color: white; border-radius: 5px; font-weight: 500; font-size: 12px; height: 26px">Chi tiết</a-button>
                                </NuxtLink>
                            </div>

                            <div>
                                <!-- Tổng công nợ -->
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px">
                                    <span style="color: #999; font-size: 13px">Tổng công nợ</span>
                                    <span style="font-weight: 600; color: #eb2f96; font-size: 17px">{{ formatCurrency(debtOverview.totalUADebt) }}</span>
                                </div>
                                <!-- Công nợ quá hạn -->
                                <div style="display: flex; justify-content: space-between; align-items: center">
                                    <span style="color: #999; font-size: 13px">Công nợ quá hạn</span>
                                    <div style="display: flex; align-items: center">
                                        <WarningOutlined style="color: #ff4d4f; font-size: 14px; margin-right: 5px" />
                                        <span style="font-weight: 600; color: #ff4d4f; font-size: 17px">{{ formatCurrency(debtOverview.overdueUADebt) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a-card>
            </a-col>
        </a-row>

        <!-- Cash Flow -->
        <!-- <a-card :bordered="true" style="margin-bottom: 24px; background: white; border: 1px solid #e8e8e8; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border-top: 4px solid #52c41a; border-radius: 8px">
            <template #title>
                <div style="display: flex; justify-content: space-between; align-items: center">
                    <div style="display: flex; align-items: center">
                        <BarChartOutlined style="margin-right: 8px; color: #52c41a; font-size: 18px" />
                        <span style="color: #52c41a; font-weight: bold; font-size: 18px">Dòng tiền</span>
                    </div>
                    <NuxtLink to="/cash-flow">
                        <a-button type="primary" size="small" style="background: #52c41a; border-color: #52c41a">Chi tiết</a-button>
                    </NuxtLink>
                </div>
            </template>

            <a-row :gutter="[24, 24]">
                <a-col :span="6">
                    <div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%); border-radius: 12px; padding: 20px; height: 120px; box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2)">
                        <div style="display: flex; align-items: center; margin-bottom: 8px">
                            <ArrowUpOutlined style="color: #2196f3; font-size: 16px; margin-right: 8px" />
                            <span style="color: #2196f3; font-size: 14px; font-weight: 500">Dòng tiền vào</span>
                        </div>
                        <div style="font-size: 24px; font-weight: bold; color: #2196f3; margin-bottom: 8px; line-height: 1">{{ formatCurrency(cashFlowOverview.currentMonthInflow) }}</div>
                    </div>
                </a-col>
                <a-col :span="6">
                    <div style="background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 50%, #ef9a9a 100%); border-radius: 12px; padding: 20px; height: 120px; box-shadow: 0 4px 12px rgba(244, 67, 54, 0.2)">
                        <div style="display: flex; align-items: center; margin-bottom: 8px">
                            <ArrowDownOutlined style="color: #f44336; font-size: 16px; margin-right: 8px" />
                            <span style="color: #f44336; font-size: 14px; font-weight: 500">Dòng tiền ra</span>
                        </div>
                        <div style="font-size: 24px; font-weight: bold; color: #f44336; margin-bottom: 8px; line-height: 1">{{ formatCurrency(cashFlowOverview.currentMonthOutflow) }}</div>
                    </div>
                </a-col>
                <a-col :span="6">
                    <div style="background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 50%, #a5d6a7 100%); border-radius: 12px; padding: 20px; height: 120px; box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2)">
                        <div style="display: flex; align-items: center; margin-bottom: 8px">
                            <RiseOutlined style="color: #4caf50; font-size: 16px; margin-right: 8px" />
                            <span style="color: #4caf50; font-size: 14px; font-weight: 500">Dòng tiền ròng</span>
                        </div>
                        <div style="font-size: 24px; font-weight: bold; color: #4caf50; margin-bottom: 8px; line-height: 1">{{ formatCurrency(cashFlowOverview.netCashFlow) }}</div>
                    </div>
                </a-col>
                <a-col :span="6">
                    <div style="background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 50%, #ce93d8 100%); border-radius: 12px; padding: 20px; height: 120px; box-shadow: 0 4px 12px rgba(156, 39, 176, 0.2)">
                        <div style="display: flex; align-items: center; margin-bottom: 8px">
                            <LineChartOutlined style="color: #9c27b0; font-size: 16px; margin-right: 8px" />
                            <span style="color: #9c27b0; font-size: 14px; font-weight: 500">Thay đổi</span>
                        </div>
                        <div style="display: flex; align-items: center">
                            <div style="font-size: 24px; font-weight: bold; color: #9c27b0; margin-right: 8px; line-height: 1">+{{ cashFlowOverview.cashFlowChange }}%</div>
                            <ArrowUpOutlined style="color: #4caf50; font-size: 16px" />
                        </div>
                    </div>
                </a-col>
            </a-row>
        </a-card> -->

        <!-- Revenue and Expense Sources Charts -->
        <a-row :gutter="[24, 24]" style="margin-bottom: 24px">
            <!-- Revenue Sources -->
            <a-col :span="12">
                <a-card :bordered="true" style="height: 100%; background: white; border: 1px solid #e8e8e8; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border-top: 4px solid #2196f3; border-radius: 8px">
                    <template #title>
                        <div style="display: flex; align-items: center">
                            <DollarOutlined style="margin-right: 8px; color: #2196f3; font-size: 18px" />
                            <span style="color: #2196f3; font-weight: bold; font-size: 18px">Chi tiết nguồn doanh thu</span>
                        </div>
                    </template>

                    <!-- Loading state -->
                    <div v-if="revenueDataLoading" style="text-align: center; padding: 40px">
                        <a-spin />
                        <p style="margin-top: 8px; color: #666; font-size: 12px">Đang tải dữ liệu doanh thu...</p>
                    </div>

                    <!-- Content -->
                    <div v-else>
                        <div style="height: 300px; margin-bottom: 16px">
                            <ClientOnly>
                                <VChart :option="revenueSourcesChart" style="height: 100%; width: 100%" autoresize />
                            </ClientOnly>
                        </div>

                        <a-table
                            :dataSource="revenueTableDataPaginated"
                            :columns="revenueSourceColumns"
                            :pagination="{
                                current: revenuePagination.current,
                                pageSize: revenuePagination.pageSize,
                                total: currentRevenueSources.length,
                                showSizeChanger: false,
                                showQuickJumper: false,
                                showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} sản phẩm`,
                                onChange: (page) => {
                                    revenuePagination.current = page;
                                },
                            }"
                            size="small"
                        >
                            <template #bodyCell="{ column, record }">
                                <template v-if="column.key === 'value'">
                                    {{ formatCurrency(record.value) }}
                                </template>
                                <template v-if="column.key === 'percentage'"> {{ record.percentage }}% </template>
                            </template>
                        </a-table>

                        <!-- Fixed total row bên ngoài pagination -->
                        <a-table :dataSource="[revenueTableTotal]" :columns="revenueSourceColumns" :pagination="false" :showHeader="false" size="small" style="margin-top: 8px; border-top: 2px solid #e8e8e8">
                            <template #bodyCell="{ column, record }">
                                <template v-if="column.key === 'name'">
                                    <span style="font-weight: bold">{{ record.name }}</span>
                                </template>
                                <template v-if="column.key === 'value'">
                                    <span style="font-weight: bold">{{ formatCurrency(record.value) }}</span>
                                </template>
                                <template v-if="column.key === 'percentage'">
                                    <span style="font-weight: bold">{{ record.percentage }}%</span>
                                </template>
                            </template>
                        </a-table>
                    </div>
                </a-card>
            </a-col>

            <!-- Expense Sources -->
            <a-col :span="12">
                <a-card :bordered="true" style="height: 100%; background: white; border: 1px solid #e8e8e8; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border-top: 4px solid #e91e63; border-radius: 8px">
                    <template #title>
                        <div style="display: flex; align-items: center">
                            <ShoppingOutlined style="margin-right: 8px; color: #e91e63; font-size: 18px" />
                            <span style="color: #e91e63; font-weight: bold; font-size: 18px">Chi tiết nguồn chi phí</span>
                        </div>
                    </template>

                    <!-- Loading state -->
                    <div v-if="expenseDataLoading" style="text-align: center; padding: 40px">
                        <a-spin />
                        <p style="margin-top: 8px; color: #666; font-size: 12px">Đang tải dữ liệu chi phí...</p>
                    </div>

                    <!-- Content -->
                    <div v-else>
                        <div style="height: 300px; margin-bottom: 16px">
                            <ClientOnly>
                                <VChart :option="expenseSourcesChart" style="height: 100%; width: 100%" autoresize />
                            </ClientOnly>
                        </div>

                        <a-table :dataSource="currentExpenseSources" :columns="expenseSourceColumns" :pagination="false" size="small">
                            <template #bodyCell="{ column, record }">
                                <template v-if="column.key === 'value'">
                                    {{ formatCurrency(record.value) }}
                                </template>
                                <template v-if="column.key === 'percentage'"> {{ record.percentage }}% </template>
                            </template>
                            <template #summary>
                                <a-table-summary-row style="font-weight: bold; border-top: 2px solid #e8e8e8">
                                    <a-table-summary-cell>Tổng cộng</a-table-summary-cell>
                                    <a-table-summary-cell style="text-align: right">{{formatCurrency(currentExpenseSources && currentExpenseSources.length > 0 ? currentExpenseSources.reduce((sum, item) => sum + item.value, 0) : 0)}}</a-table-summary-cell>
                                    <a-table-summary-cell style="text-align: right">100%</a-table-summary-cell>
                                </a-table-summary-row>
                            </template>
                        </a-table>
                    </div>
                </a-card>
            </a-col>
        </a-row>

        <!-- Financial Alerts -->
        <a-card :bordered="true" style="background: white; border: 1px solid #e8e8e8; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border-top: 4px solid #ff9800; border-radius: 8px">
            <template #title>
                <div style="display: flex; justify-content: space-between; align-items: center">
                    <div style="display: flex; align-items: center">
                        <WarningOutlined style="margin-right: 8px; color: #ff9800; font-size: 18px" />
                        <span style="color: #ff9800; font-weight: bold; font-size: 18px">Cảnh báo tài chính</span>
                    </div>
                    <div v-if="!financialAlertsLoading && financialAlerts.summary.total > 0" style="display: flex; gap: 8px; align-items: center">
                        <a-badge :count="financialAlerts.summary.critical" style="background-color: #f5222d" v-if="financialAlerts.summary.critical > 0" />
                        <a-badge :count="financialAlerts.summary.high" style="background-color: #fa8c16" v-if="financialAlerts.summary.high > 0" />
                        <span v-if="financialAlerts.summary.actionRequired > 0" style="color: #666; font-size: 12px">{{ financialAlerts.summary.actionRequired }} yêu cầu xử lý</span>
                    </div>
                </div>
            </template>

            <!-- Loading state -->
            <div v-if="financialAlertsLoading" style="text-align: center; padding: 40px">
                <a-spin />
                <p style="margin-top: 8px; color: #666; font-size: 12px">Đang tải cảnh báo tài chính...</p>
            </div>

            <!-- No alerts -->
            <div v-else-if="financialAlerts.alerts.length === 0" style="text-align: center; padding: 40px">
                <div style="color: #52c41a; font-size: 48px; margin-bottom: 16px">
                    <DollarCircleOutlined />
                </div>
                <h3 style="color: #52c41a; margin-bottom: 8px">Tình hình tài chính ổn định</h3>
                <p style="color: #666; margin: 0">Không có cảnh báo tài chính nào cần xử lý.</p>
            </div>

            <!-- Alerts list -->
            <div v-else style="margin-top: 16px">
                <div v-for="(alert, index) in financialAlerts.alerts" :key="alert.id" :style="`margin-bottom: ${index === financialAlerts.alerts.length - 1 ? '0' : '8px'}`">
                    <div :style="getAlertStyle(alert)" class="custom-alert">
                        <div class="alert-content">
                            <div class="alert-header">
                                <div class="alert-icon">
                                    <WarningOutlined v-if="alert.type === 'error'" />
                                    <ExclamationCircleOutlined v-else-if="alert.type === 'warning'" />
                                    <InfoCircleOutlined v-else />
                                </div>
                                <div class="alert-title">{{ alert.title }}</div>
                                <div v-if="alert.link" class="alert-action">
                                    <NuxtLink :to="alert.link">
                                        <a-button size="small" type="link" style="padding: 0; height: auto">Xem chi tiết</a-button>
                                    </NuxtLink>
                                </div>
                            </div>
                            <div class="alert-description">{{ alert.description }}</div>
                        </div>
                    </div>
                </div>

                <!-- Summary info -->
                <div v-if="financialAlerts.summary.total > financialAlerts.alerts.length" style="margin-top: 16px; padding: 12px; background: #f5f5f5; border-radius: 6px; text-align: center">
                    <span style="color: #666; font-size: 12px"> Hiển thị {{ financialAlerts.alerts.length }} trong tổng số {{ financialAlerts.summary.total }} cảnh báo </span>
                </div>
            </div>
        </a-card>
    </div>
</template>

<script setup lang="ts">
import { ArrowUpOutlined, ArrowDownOutlined, BankOutlined, DollarCircleOutlined, BarChartOutlined, DollarOutlined, ShoppingOutlined, RiseOutlined, LineChartOutlined, WarningOutlined, ExclamationCircleOutlined, InfoCircleOutlined } from "@ant-design/icons-vue";
import { formatUSD } from "~/utils/formatters";
import { ref, computed, onMounted, onUnmounted, watch } from "vue";

// Composables
const { createPieChart } = useCharts();

// State - Mặc định theo tháng trước (không cho phép chọn tương lai)
const currentDate = new Date();
const currentMonth = currentDate.getMonth() + 1; // 1-12
const currentYear = currentDate.getFullYear();

// Tính tháng trước
const previousMonth = currentMonth === 1 ? 12 : currentMonth - 1;
const previousYear = currentMonth === 1 ? currentYear - 1 : currentYear;
const previousQuarter = Math.ceil(previousMonth / 3);

const timeFrame = ref("month");
const period = ref("current");
const selectedMonth = ref(previousMonth.toString());
const selectedQuarter = ref(previousQuarter.toString());
const selectedYear = ref(previousYear.toString());
const productType = ref("all");

// Expense data from API
const expenseDataFromAPI = ref([]);
const expenseDataLoading = ref(false);

// Revenue data from API
const revenueDataFromAPI = ref([]);
const revenueDataLoading = ref(false);

// Financial overview data from API
const financialOverviewFromAPI = ref(null);
const financialOverviewLoading = ref(false);

// Pagination state cho revenue table
const revenuePagination = ref({
    current: 1,
    pageSize: 11,
    showSizeChanger: false,
    showQuickJumper: false,
    showTotal: (total: number, range: number[]) => `${range[0]}-${range[1]} của ${total} sản phẩm`,
});

// Financial data structure (same as Dashboard.js)
const financialData = {
    month: {
        current: {
            totalRevenue: 10300000000,
            totalExpenses: 6600000000,
            totalProfit: 3700000000,
            profitMargin: 35.9,
            revenueChange: 12.5,
            expensesChange: 5.2,
            profitChange: 8.3,
            marginChange: -2.1,
            revenueSources: [
                { name: "Game - In-app Purchase", value: 4500000000, percentage: 43.7 },
                { name: "Game - Quảng cáo", value: 2800000000, percentage: 27.2 },
                { name: "App - Subscription", value: 1700000000, percentage: 16.5 },
                { name: "App - Quảng cáo", value: 950000000, percentage: 9.2 },
                { name: "Dịch vụ tư vấn", value: 350000000, percentage: 3.4 },
            ],
            expenseSources: [
                { name: "Nhân sự", value: 2800000000, percentage: 42.4 },
                { name: "Marketing - UA", value: 1450000000, percentage: 22.0 },
                { name: "Marketing - Branding", value: 650000000, percentage: 9.8 },
                { name: "Vận hành máy chủ", value: 750000000, percentage: 11.4 },
                { name: "Thuê văn phòng", value: 450000000, percentage: 6.8 },
                { name: "Phí dịch vụ bên thứ 3", value: 350000000, percentage: 5.3 },
                { name: "Chi phí khác", value: 150000000, percentage: 2.3 },
            ],
        },
        previous: {
            totalRevenue: 9200000000,
            totalExpenses: 6000000000,
            totalProfit: 3200000000,
            profitMargin: 34.8,
            revenueChange: 10.2,
            expensesChange: 4.8,
            profitChange: 7.5,
            marginChange: -1.8,
            revenueSources: [
                { name: "Game - In-app Purchase", value: 4100000000, percentage: 44.6 },
                { name: "Game - Quảng cáo", value: 2500000000, percentage: 27.2 },
                { name: "App - Subscription", value: 1400000000, percentage: 15.2 },
                { name: "App - Quảng cáo", value: 900000000, percentage: 9.8 },
                { name: "Dịch vụ tư vấn", value: 300000000, percentage: 3.2 },
            ],
            expenseSources: [
                { name: "Nhân sự", value: 2600000000, percentage: 43.3 },
                { name: "Marketing - UA", value: 1300000000, percentage: 21.7 },
                { name: "Marketing - Branding", value: 600000000, percentage: 10.0 },
                { name: "Vận hành máy chủ", value: 650000000, percentage: 10.8 },
                { name: "Thuê văn phòng", value: 450000000, percentage: 7.5 },
                { name: "Phí dịch vụ bên thứ 3", value: 300000000, percentage: 5.0 },
                { name: "Chi phí khác", value: 100000000, percentage: 1.7 },
            ],
        },
    },
    quarter: {
        current: {
            totalRevenue: **********0,
            totalExpenses: 19000000000,
            totalProfit: 11000000000,
            profitMargin: 36.7,
            revenueChange: 15.0,
            expensesChange: 8.5,
            profitChange: 10.2,
            marginChange: -1.5,
            revenueSources: [
                { name: "Game - In-app Purchase", value: 13200000000, percentage: 44.0 },
                { name: "Game - Quảng cáo", value: 8100000000, percentage: 27.0 },
                { name: "App - Subscription", value: 4800000000, percentage: 16.0 },
                { name: "App - Quảng cáo", value: 2700000000, percentage: 9.0 },
                { name: "Dịch vụ tư vấn", value: 1200000000, percentage: 4.0 },
            ],
            expenseSources: [
                { name: "Nhân sự", value: 8000000000, percentage: 42.1 },
                { name: "Marketing - UA", value: 4200000000, percentage: 22.1 },
                { name: "Marketing - Branding", value: 1900000000, percentage: 10.0 },
                { name: "Vận hành máy chủ", value: 2100000000, percentage: 11.1 },
                { name: "Thuê văn phòng", value: 1350000000, percentage: 7.1 },
                { name: "Phí dịch vụ bên thứ 3", value: 950000000, percentage: 5.0 },
                { name: "Chi phí khác", value: 500000000, percentage: 2.6 },
            ],
        },
        previous: {
            totalRevenue: 26000000000,
            totalExpenses: 17000000000,
            totalProfit: 9000000000,
            profitMargin: 34.6,
            revenueChange: 12.0,
            expensesChange: 7.0,
            profitChange: 9.0,
            marginChange: -1.0,
            revenueSources: [
                { name: "Game - In-app Purchase", value: 11400000000, percentage: 43.8 },
                { name: "Game - Quảng cáo", value: 7000000000, percentage: 26.9 },
                { name: "App - Subscription", value: 4200000000, percentage: 16.2 },
                { name: "App - Quảng cáo", value: 2400000000, percentage: 9.2 },
                { name: "Dịch vụ tư vấn", value: 1000000000, percentage: 3.9 },
            ],
            expenseSources: [
                { name: "Nhân sự", value: 7200000000, percentage: 42.4 },
                { name: "Marketing - UA", value: 3700000000, percentage: 21.8 },
                { name: "Marketing - Branding", value: 1700000000, percentage: 10.0 },
                { name: "Vận hành máy chủ", value: 1900000000, percentage: 11.2 },
                { name: "Thuê văn phòng", value: 1200000000, percentage: 7.0 },
                { name: "Phí dịch vụ bên thứ 3", value: 850000000, percentage: 5.0 },
                { name: "Chi phí khác", value: 450000000, percentage: 2.6 },
            ],
        },
    },
    year: {
        current: {
            totalRevenue: 120000000000,
            totalExpenses: 75000000000,
            totalProfit: 45000000000,
            profitMargin: 37.5,
            revenueChange: 22.0,
            expensesChange: 15.0,
            profitChange: 18.0,
            marginChange: 2.5,
            revenueSources: [
                { name: "Game - In-app Purchase", value: 52800000000, percentage: 44.0 },
                { name: "Game - Quảng cáo", value: 32400000000, percentage: 27.0 },
                { name: "App - Subscription", value: 19200000000, percentage: 16.0 },
                { name: "App - Quảng cáo", value: 10800000000, percentage: 9.0 },
                { name: "Dịch vụ tư vấn", value: 4800000000, percentage: 4.0 },
            ],
            expenseSources: [
                { name: "Nhân sự", value: **********0, percentage: 42.0 },
                { name: "Marketing - UA", value: 16500000000, percentage: 22.0 },
                { name: "Marketing - Branding", value: 7500000000, percentage: 10.0 },
                { name: "Vận hành máy chủ", value: 8250000000, percentage: 11.0 },
                { name: "Thuê văn phòng", value: 5250000000, percentage: 7.0 },
                { name: "Phí dịch vụ bên thứ 3", value: 3750000000, percentage: 5.0 },
                { name: "Chi phí khác", value: 2250000000, percentage: 3.0 },
            ],
        },
        previous: {
            totalRevenue: 98000000000,
            totalExpenses: **********0,
            totalProfit: 35000000000,
            profitMargin: 35.7,
            revenueChange: 18.0,
            expensesChange: 12.0,
            profitChange: 15.0,
            marginChange: 1.5,
            revenueSources: [
                { name: "Game - In-app Purchase", value: 43120000000, percentage: 44.0 },
                { name: "Game - Quảng cáo", value: 26460000000, percentage: 27.0 },
                { name: "App - Subscription", value: 15680000000, percentage: 16.0 },
                { name: "App - Quảng cáo", value: 8820000000, percentage: 9.0 },
                { name: "Dịch vụ tư vấn", value: 3920000000, percentage: 4.0 },
            ],
            expenseSources: [
                { name: "Nhân sự", value: 26460000000, percentage: 42.0 },
                { name: "Marketing - UA", value: 13860000000, percentage: 22.0 },
                { name: "Marketing - Branding", value: **********, percentage: 10.0 },
                { name: "Vận hành máy chủ", value: **********, percentage: 11.0 },
                { name: "Thuê văn phòng", value: **********, percentage: 7.0 },
                { name: "Phí dịch vụ bên thứ 3", value: **********, percentage: 5.0 },
                { name: "Chi phí khác", value: **********, percentage: 3.0 },
            ],
        },
    },
} as const;

// Bank balances data - sẽ được lấy từ API
const bankBalances = ref({
    totalBalance: 0,
    accountTypes: [] as Array<{
        account_type: string;
        account_name: string;
        balance: number;
        color: string;
    }>,
    currency: "USD",
    last_updated_date: null as string | null,
});

// Loading state cho bank balances
const bankBalancesLoading = ref(false);

// Debt overview data - sẽ được lấy từ API
const debtOverview = ref({
    totalMonetizationDebt: 0,
    overdueMonetizationDebt: 0,
    totalUADebt: 0,
    overdueUADebt: 0,
});

// Loading state cho debt overview
const debtOverviewLoading = ref(false);

// Financial alerts data - sẽ được lấy từ API
const financialAlerts = ref({
    alerts: [] as Array<{
        id: string;
        type: "success" | "info" | "warning" | "error";
        title: string;
        description: string;
        severity: string;
        category: string;
        actionRequired: boolean;
        link?: string;
    }>,
    summary: {
        total: 0,
        critical: 0,
        high: 0,
        medium: 0,
        actionRequired: 0,
    },
});

// Loading state cho financial alerts
const financialAlertsLoading = ref(false);

// Cash flow overview data
const cashFlowOverview = {
    currentMonthInflow: **********,
    currentMonthOutflow: **********,
    netCashFlow: 800000000,
    cashFlowChange: 5.3,
};

// Computed
const financialOverview = computed(() => {
    // Sử dụng dữ liệu từ API nếu có, fallback về mock data
    if (financialOverviewFromAPI.value) {
        return financialOverviewFromAPI.value;
    }

    // Fallback về mock data nếu API chưa có dữ liệu
    return financialData[timeFrame.value as keyof typeof financialData][period.value as keyof typeof financialData.month];
});

const currentRevenueSources = computed(() => {
    // Sử dụng dữ liệu từ API nếu có, fallback về mock data
    if (revenueDataFromAPI.value.length > 0) {
        return revenueDataFromAPI.value.map((item) => ({
            name: item.product || "Sản phẩm khác",
            value: item.revenue || 0,
            percentage: item.percentage || 0,
        }));
    }

    // Fallback về mock data nếu API chưa có dữ liệu
    return financialOverview.value.revenueSources;
});

const currentExpenseSources = computed(() => {
    // Sử dụng dữ liệu từ API nếu có, fallback về mock data
    if (expenseDataFromAPI.value.length > 0) {
        return expenseDataFromAPI.value.map((item) => ({
            name: item.name || "Khác",
            value: item.value || 0,
            percentage: item.percentage || 0,
        }));
    }

    // Fallback về mock data nếu API chưa có dữ liệu
    return financialOverview.value.expenseSources;
});

// Available years - không cho phép chọn năm tương lai
const availableYears = computed(() => {
    const baseYears = [2022, 2023, 2024];

    // Chỉ thêm năm hiện tại, không thêm năm tương lai
    if (!baseYears.includes(currentYear)) {
        baseYears.push(currentYear);
        baseYears.sort((a, b) => a - b);
    }

    // Lọc ra các năm tương lai
    return baseYears.filter(year => year <= currentYear);
});

// Available months - không cho phép chọn tháng tương lai trong năm hiện tại
const availableMonths = computed(() => {
    const allMonths = [
        { value: "1", label: "Tháng 1" },
        { value: "2", label: "Tháng 2" },
        { value: "3", label: "Tháng 3" },
        { value: "4", label: "Tháng 4" },
        { value: "5", label: "Tháng 5" },
        { value: "6", label: "Tháng 6" },
        { value: "7", label: "Tháng 7" },
        { value: "8", label: "Tháng 8" },
        { value: "9", label: "Tháng 9" },
        { value: "10", label: "Tháng 10" },
        { value: "11", label: "Tháng 11" },
        { value: "12", label: "Tháng 12" },
    ];

    // Nếu là năm hiện tại, chỉ cho phép chọn tháng từ trước đến hiện tại
    if (parseInt(selectedYear.value) === currentYear) {
        return allMonths.filter(month => parseInt(month.value) <= currentMonth);
    }

    // Nếu là năm trước, cho phép chọn tất cả tháng
    return allMonths;
});

// Available quarters - không cho phép chọn quý tương lai trong năm hiện tại  
const availableQuarters = computed(() => {
    const allQuarters = [
        { value: "1", label: "Quý 1" },
        { value: "2", label: "Quý 2" },
        { value: "3", label: "Quý 3" },
        { value: "4", label: "Quý 4" },
    ];

    // Nếu là năm hiện tại, chỉ cho phép chọn quý từ trước đến hiện tại
    if (parseInt(selectedYear.value) === currentYear) {
        const currentQuarter = Math.ceil(currentMonth / 3);
        return allQuarters.filter(quarter => parseInt(quarter.value) <= currentQuarter);
    }

    // Nếu là năm trước, cho phép chọn tất cả quý
    return allQuarters;
});

// API Base URL
const config = useRuntimeConfig();
const apiBase = config.public.apiBase;

// Composables
const { getOverviewDebts, getOverviewBankBalances, getFinancialAlerts } = useApi();

// Fetch bank balances từ API
const fetchBankBalances = async () => {
    bankBalancesLoading.value = true;
    try {
        const response = await getOverviewBankBalances();
        if (response.success) {
            bankBalances.value = response.data;
        }
    } catch (error) {
        console.error("Lỗi khi lấy số dư tài khoản:", error);
    } finally {
        bankBalancesLoading.value = false;
    }
};

// Fetch debt overview từ API
const fetchDebtOverview = async () => {
    debtOverviewLoading.value = true;
    try {
        const params = {
            timeFrame: timeFrame.value,
            period: period.value,
            selectedMonth: selectedMonth.value,
            selectedYear: selectedYear.value,
            selectedQuarter: selectedQuarter.value,
            productType: productType.value,
        };

        const response = await getOverviewDebts(params);

        if (response.success) {
            debtOverview.value = {
                totalMonetizationDebt: response.data.monetization.totalDebt,
                overdueMonetizationDebt: response.data.monetization.overdueDebt,
                totalUADebt: response.data.ua.totalDebt,
                overdueUADebt: response.data.ua.overdueDebt,
            };
        }
    } catch (error) {
        console.error("Lỗi khi lấy tổng quan công nợ:", error);
    } finally {
        debtOverviewLoading.value = false;
    }
};

// Fetch financial alerts từ API
const fetchFinancialAlerts = async () => {
    financialAlertsLoading.value = true;
    try {
        const response = await getFinancialAlerts();
        console.log("🚨 Financial Alerts API Response:", response); // Debug log
        if (response.success) {
            financialAlerts.value = {
                alerts: response.data.alerts || [],
                summary: response.data.summary || {
                    total: 0,
                    critical: 0,
                    high: 0,
                    medium: 0,
                    actionRequired: 0,
                },
            };
            console.log("🚨 Processed Financial Alerts:", financialAlerts.value); // Debug log
        }
    } catch (error) {
        console.error("Lỗi khi lấy cảnh báo tài chính:", error);
    } finally {
        financialAlertsLoading.value = false;
    }
};

// Fetch expense data từ API
const fetchExpenseData = async () => {
    expenseDataLoading.value = true;
    try {
        const params = {
            timeFrame: timeFrame.value,
            period: period.value,
            selectedMonth: selectedMonth.value,
            selectedYear: selectedYear.value,
            selectedQuarter: selectedQuarter.value,
        };

        const response = await $fetch(`${apiBase}/api/overview/get_expense_details`, { query: params });

        if (response.success) {
            expenseDataFromAPI.value = response.data || [];
        }
    } catch (error) {
        console.error("Lỗi khi lấy dữ liệu chi phí:", error);
        expenseDataFromAPI.value = [];
    } finally {
        expenseDataLoading.value = false;
    }
};

// Fetch revenue data từ API
const fetchRevenueData = async () => {
    revenueDataLoading.value = true;
    try {
        const params = {
            timeFrame: timeFrame.value,
            period: period.value,
            month: parseInt(selectedMonth.value),
            quarter: parseInt(selectedQuarter.value),
            year: parseInt(selectedYear.value),
            productType: productType.value,
        };

        const response = await $fetch(`${apiBase}/api/revenue/by-product`, { query: params });

        if (response.success) {
            revenueDataFromAPI.value = response.data || [];
        }
    } catch (error) {
        console.error("Lỗi khi lấy dữ liệu doanh thu:", error);
        revenueDataFromAPI.value = [];
    } finally {
        revenueDataLoading.value = false;
    }
};

// Fetch financial overview data từ API
const fetchFinancialOverview = async () => {
    financialOverviewLoading.value = true;
    try {
        const params = {
            timeFrame: timeFrame.value,
            period: period.value,
            selectedMonth: selectedMonth.value,
            selectedQuarter: selectedQuarter.value,
            selectedYear: selectedYear.value,
            productType: productType.value,
        };

        const response = await $fetch(`${apiBase}/api/overview/get_financial_overview`, { query: params });

        if (response.success) {
            financialOverviewFromAPI.value = response.data;
        }
    } catch (error) {
        console.error("Lỗi khi lấy tổng quan tài chính:", error);
        financialOverviewFromAPI.value = null;
    } finally {
        financialOverviewLoading.value = false;
    }
};

// Format currency function
const formatCurrency = (value: number): string => {
    return formatUSD(value);
};

// Get alert style based on debt type
const getAlertStyle = (alert: any) => {
    const baseStyle = {
        padding: "12px 16px",
        borderRadius: "8px",
        border: "1px solid",
        marginBottom: "8px",
    };

    // Phân biệt màu theo loại công nợ
    if (alert.debtType === "MONETIZATION") {
        return {
            ...baseStyle,
            background: "linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%)",
            borderColor: "#ffa940",
            borderLeft: "4px solid #fa8c16",
        };
    } else if (alert.debtType === "USER_ACQUISITION") {
        return {
            ...baseStyle,
            background: "linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%)",
            borderColor: "#ff7875",
            borderLeft: "4px solid #ff4d4f",
        };
    } else if (alert.debtType === "SUMMARY") {
        return {
            ...baseStyle,
            background: "linear-gradient(135deg, #e6f4ff 0%, #bae0ff 100%)",
            borderColor: "#40a9ff",
            borderLeft: "4px solid #1890ff",
        };
    } else {
        // Default style cho các alert khác
        if (alert.type === "error") {
            return {
                ...baseStyle,
                background: "linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%)",
                borderColor: "#ff7875",
                borderLeft: "4px solid #ff4d4f",
            };
        } else if (alert.type === "warning") {
            return {
                ...baseStyle,
                background: "linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%)",
                borderColor: "#ffd666",
                borderLeft: "4px solid #faad14",
            };
        } else {
            return {
                ...baseStyle,
                background: "linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)",
                borderColor: "#95de64",
                borderLeft: "4px solid #52c41a",
            };
        }
    }
};

// Time frame label
const getTimeFrameLabel = () => {
    if (timeFrame.value === "month") {
        if (period.value === "current") {
            return `Tháng ${selectedMonth.value}/${selectedYear.value}`;
        } else {
            const prevMonth = selectedMonth.value === "1" ? "12" : String(parseInt(selectedMonth.value) - 1);
            const prevYear = selectedMonth.value === "1" ? String(parseInt(selectedYear.value) - 1) : selectedYear.value;
            return `Tháng ${prevMonth}/${prevYear}`;
        }
    } else if (timeFrame.value === "quarter") {
        if (period.value === "current") {
            return `Quý ${selectedQuarter.value}/${selectedYear.value}`;
        } else {
            const prevQuarter = selectedQuarter.value === "1" ? "4" : String(parseInt(selectedQuarter.value) - 1);
            const prevYear = selectedQuarter.value === "1" ? String(parseInt(selectedYear.value) - 1) : selectedYear.value;
            return `Quý ${prevQuarter}/${prevYear}`;
        }
    } else {
        if (period.value === "current") {
            return `Năm ${selectedYear.value}`;
        } else {
            return `Năm ${parseInt(selectedYear.value) - 1}`;
        }
    }
};

// Charts
const revenueSourcesChart = computed(() => {
    // Kiểm tra có dữ liệu trước khi xử lý
    if (!currentRevenueSources.value || currentRevenueSources.value.length === 0) {
        // Trả về empty chart nếu không có dữ liệu
        return {
            title: {
                text: "Chi tiết nguồn doanh thu",
                left: "center",
                textStyle: {
                    fontSize: 16,
                    fontWeight: "bold",
                },
            },
            series: [
                {
                    type: "pie",
                    radius: ["40%", "70%"],
                    data: [],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: "rgba(0, 0, 0, 0.5)",
                        },
                    },
                },
            ],
        };
    }

    // Lấy top 10 items có revenue cao nhất
    const top10Items = [...currentRevenueSources.value].sort((a, b) => b.value - a.value).slice(0, 10);

    const baseChart = createPieChart(
        top10Items.map((item) => ({
            name: item.name,
            value: item.value,
        })),
        "Chi tiết nguồn doanh thu"
    );

    // Override label configuration để chỉ hiển thị phần trăm
    if (baseChart.series && baseChart.series[0]) {
        baseChart.series[0].label = {
            show: true,
            position: "outside",
            formatter: "{d}%",
            fontSize: 12,
        };
        baseChart.series[0].labelLine = {
            show: true,
            length: 10,
            length2: 5,
        };
    }

    return baseChart;
});

const expenseSourcesChart = computed(() => {
    // Kiểm tra có dữ liệu trước khi tạo chart
    if (!currentExpenseSources.value || currentExpenseSources.value.length === 0) {
        // Trả về empty chart nếu không có dữ liệu
        return {
            title: {
                text: "Chi tiết nguồn chi phí",
                left: "center",
                textStyle: {
                    fontSize: 16,
                    fontWeight: "bold",
                },
            },
            series: [
                {
                    type: "pie",
                    radius: ["40%", "70%"],
                    data: [],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: "rgba(0, 0, 0, 0.5)",
                        },
                    },
                },
            ],
        };
    }

    return createPieChart(
        currentExpenseSources.value.map((item) => ({
            name: item.name,
            value: item.value,
        })),
        "Chi tiết nguồn chi phí"
    );
});

// Table columns
const revenueSourceColumns = [
    {
        title: "Sản phẩm",
        dataIndex: "name",
        key: "name",
    },
    {
        title: "Doanh thu",
        dataIndex: "value",
        key: "value",
        align: "right" as const,
    },
    {
        title: "Tỷ lệ (%)",
        dataIndex: "percentage",
        key: "percentage",
        align: "right" as const,
    },
];

const expenseSourceColumns = [
    {
        title: "Nguồn chi phí",
        dataIndex: "name",
        key: "name",
    },
    {
        title: "Chi phí",
        dataIndex: "value",
        key: "value",
        align: "right" as const,
    },
    {
        title: "Tỷ lệ (%)",
        dataIndex: "percentage",
        key: "percentage",
        align: "right" as const,
    },
];

// Event handlers
const handleTimeFrameChange = (value: string) => {
    timeFrame.value = value;
};

const handlePeriodChange = (value: string) => {
    period.value = value;
};

const handleMonthChange = (value: string) => {
    selectedMonth.value = value;
};

const handleQuarterChange = (value: string) => {
    selectedQuarter.value = value;
};

const handleYearChange = (value: string) => {
    selectedYear.value = value;

    // Nếu chọn năm hiện tại, cần kiểm tra và điều chỉnh tháng/quý
    if (parseInt(value) === currentYear) {
        // Điều chỉnh tháng nếu vượt quá tháng hiện tại
        if (parseInt(selectedMonth.value) > currentMonth) {
            selectedMonth.value = currentMonth.toString();
        }

        // Điều chỉnh quý nếu vượt quá quý hiện tại
        const currentQuarter = Math.ceil(currentMonth / 3);
        if (parseInt(selectedQuarter.value) > currentQuarter) {
            selectedQuarter.value = currentQuarter.toString();
        }
    }
};

const handleProductTypeChange = (value: string) => {
    productType.value = value;
};

// Computed cho paginated revenue data
const revenueTableDataPaginated = computed(() => {
    if (!currentRevenueSources.value || currentRevenueSources.value.length === 0) {
        return [];
    }
    const start = (revenuePagination.value.current - 1) * revenuePagination.value.pageSize;
    const end = start + revenuePagination.value.pageSize;
    return currentRevenueSources.value.slice(start, end);
});

// Computed cho revenue total row
const revenueTableTotal = computed(() => ({
    name: "Tổng cộng",
    value: currentRevenueSources.value && currentRevenueSources.value.length > 0 ? currentRevenueSources.value.reduce((sum, item) => sum + item.value, 0) : 0,
    percentage: 100,
}));

// SEO
useHead({
    title: "Tổng quan tài chính - Dmobin Finance",
    meta: [
        {
            name: "description",
            content: "Dashboard tài chính tổng quan của công ty Dmobin",
        },
    ],
});

// Page meta
definePageMeta({
    middleware: "auth",
});

// Watch bộ lọc thời gian để tự động fetch debt overview
watch(
    [timeFrame, period, selectedMonth, selectedYear, selectedQuarter, productType],
    () => {
        fetchExpenseData();
        fetchRevenueData();
        fetchFinancialOverview();
    },
    { immediate: true }
);

// Fetch data khi component mounted
onMounted(() => {
    fetchFinancialAlerts();
    fetchExpenseData();
    fetchRevenueData();
    fetchFinancialOverview();

    // Auto refresh financial alerts every 5 minutes
    const alertsInterval = setInterval(() => {
        fetchFinancialAlerts();
    }, 5 * 60 * 1000);

    // Cleanup interval on unmount
    onUnmounted(() => {
        clearInterval(alertsInterval);
    });
});
</script>

<style scoped>
.custom-alert {
    transition: all 0.3s ease;
}

.custom-alert:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.alert-content {
    width: 100%;
}

.alert-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
}

.alert-icon {
    font-size: 16px;
    margin-right: 8px;
    margin-top: 2px;
    color: inherit;
}

.alert-title {
    flex: 1;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.4;
}

.alert-action {
    margin-left: 12px;
}

.alert-description {
    font-size: 13px;
    line-height: 1.5;
    color: rgba(0, 0, 0, 0.75);
    margin-left: 24px;
}
</style>
