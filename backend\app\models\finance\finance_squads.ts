import { BaseModel, column, beforeSave } from '@adonisjs/lucid/orm'
import Helper from '#libraries/vcms/helper'

export default class FinanceSquads extends BaseModel {
    public static table = 'finance_squads'

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare name: string

    @column()
    declare slug: string

    @column()
    declare description: string | null

    @column()
    declare status: 'active' | 'inactive' | 'archived'

    @column()
    declare type: number | null

    @column()
    declare created: number

    @column()
    declare updated: number

    @beforeSave()
    public static async autoTimestamp(data: FinanceSquads) {
        if (!data.created) {
            data.created = Helper.now_timestamp()
        }
        data.updated = Helper.now_timestamp()
    }
} 