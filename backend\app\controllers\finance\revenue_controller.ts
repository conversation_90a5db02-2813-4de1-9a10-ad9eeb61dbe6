// import type { HttpContext } from '@adonisjs/core/http'

import { Get, ApiOnly } from '#libraries/vcms/route_decorators'
import { HttpContext } from '@adonisjs/core/http'
import Database from '@adonisjs/lucid/services/db'

@ApiOnly()
export default class RevenueController {
    /**
     * Lấy danh sách squad
     */
    @Get('/api/revenue/squads')
    public async getSquads({ response }: HttpContext) {
        try {
            const squads = await Database
                .from('finance_squads')
                .where('status', 'active')
                .select(['id', 'name', 'slug', 'description'])
                .orderBy('name', 'asc')

            return response.json({
                success: true,
                data: squads
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy danh sách squad',
                error: error.message
            })
        }
    }

    /**
     * Lấy danh sách projects
     */
    @Get('/api/revenue/projects')
    public async getProjects({ response }: HttpContext) {
        try {
            const projects = await Database
                .from('finance_project as p')
                .leftJoin('finance_squads as s', 'p.squad_id', 's.id')
                .select([
                    'p.id',
                    'p.name as project_name',
                    's.name as squad_name',
                    'p.type'
                ])
                .orderBy('p.id', 'asc')

            return response.json({
                success: true,
                data: projects
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy danh sách project',
                error: error.message
            })
        }
    }

    /**
     * Lấy tổng quan doanh thu
     */
    @Get('/api/revenue/overview')
    public async getRevenueOverview({ request, response }: HttpContext) {
        try {
            const { timeFrame = 'month', period = 'current', month = 6, quarter = 2, year = 2025 } = request.qs()

            // Tính toán khoảng thời gian
            const { startDate, endDate, prevStartDate, prevEndDate } = this.calculateDateRange(timeFrame, period, month, quarter, year)

            // Tính toán tháng, quý, năm từ startDate và endDate để lọc trong bảng finance_revenue
            const startYear = new Date(startDate).getFullYear()
            const startMonth = new Date(startDate).getMonth() + 1
            const startQuarter = Math.ceil(startMonth / 3)
            const endYear = new Date(endDate).getFullYear()
            const endMonth = new Date(endDate).getMonth() + 1
            const endQuarter = Math.ceil(endMonth / 3)

            const prevStartYear = new Date(prevStartDate).getFullYear()
            const prevStartMonth = new Date(prevStartDate).getMonth() + 1
            const prevStartQuarter = Math.ceil(prevStartMonth / 3)
            const prevEndYear = new Date(prevEndDate).getFullYear()
            const prevEndMonth = new Date(prevEndDate).getMonth() + 1
            const prevEndQuarter = Math.ceil(prevEndMonth / 3)

            // Lấy doanh thu hiện tại từ finance_revenue
            let currentQuery = Database.from('finance_revenue')
            if (timeFrame === 'month') {
                if (startYear === endYear) {
                    currentQuery = currentQuery
                        .where('year', startYear)
                        .whereBetween('month', [startMonth, endMonth])
                } else {
                    currentQuery = currentQuery
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('year', startYear).where('month', '>=', startMonth)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('year', endYear).where('month', '<=', endMonth)
                            })
                        })
                }
            } else if (timeFrame === 'quarter') {
                if (startYear === endYear) {
                    currentQuery = currentQuery
                        .where('year', startYear)
                        .whereBetween('quarter', [startQuarter, endQuarter])
                } else {
                    currentQuery = currentQuery
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('year', startYear).where('quarter', '>=', startQuarter)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('year', endYear).where('quarter', '<=', endQuarter)
                            })
                        })
                }
            } else {
                currentQuery = currentQuery.whereBetween('year', [startYear, endYear])
            }

            const currentRevenue = await currentQuery
                .sum('value as total')
                .first()

            // Lấy doanh thu kỳ trước
            let previousQuery = Database.from('finance_revenue')
            if (timeFrame === 'month') {
                if (prevStartYear === prevEndYear) {
                    previousQuery = previousQuery
                        .where('year', prevStartYear)
                        .whereBetween('month', [prevStartMonth, prevEndMonth])
                } else {
                    previousQuery = previousQuery
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('year', prevStartYear).where('month', '>=', prevStartMonth)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('year', prevEndYear).where('month', '<=', prevEndMonth)
                            })
                        })
                }
            } else if (timeFrame === 'quarter') {
                if (prevStartYear === prevEndYear) {
                    previousQuery = previousQuery
                        .where('year', prevStartYear)
                        .whereBetween('quarter', [prevStartQuarter, prevEndQuarter])
                } else {
                    previousQuery = previousQuery
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('year', prevStartYear).where('quarter', '>=', prevStartQuarter)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('year', prevEndYear).where('quarter', '<=', prevEndQuarter)
                            })
                        })
                }
            } else {
                previousQuery = previousQuery.whereBetween('year', [prevStartYear, prevEndYear])
            }

            const previousRevenue = await previousQuery
                .sum('value as total')
                .first()

            const currentTotal = currentRevenue?.total || 0
            const previousTotal = previousRevenue?.total || 0
            const changePercent = previousTotal > 0 ? ((currentTotal - previousTotal) / previousTotal * 100) : 0

            return response.json({
                success: true,
                data: {
                    totalRevenue: currentTotal,
                    previousRevenue: previousTotal,
                    revenueChange: Math.round(changePercent * 100) / 100,
                    period: {
                        current: { startDate, endDate },
                        previous: { startDate: prevStartDate, endDate: prevEndDate }
                    }
                }
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy tổng quan doanh thu',
                error: error.message
            })
        }
    }

    /**
     * Lấy phân bố doanh thu theo Squad
     */
    @Get('/api/revenue/by-squad')
    public async getRevenueBySquad({ request, response }: HttpContext) {
        console.log('getRevenueBySquad');
        try {
            const { timeFrame = 'month', period = 'current', month = 6, quarter = 2, year = 2025 } = request.qs()
            const { startDate, endDate } = this.calculateDateRange(timeFrame, period, month, quarter, year)

            // Tính toán tháng, quý, năm từ startDate và endDate để lọc trong bảng finance_revenue
            const startYear = new Date(startDate).getFullYear()
            const startMonth = new Date(startDate).getMonth() + 1
            const startQuarter = Math.ceil(startMonth / 3)
            const endYear = new Date(endDate).getFullYear()
            const endMonth = new Date(endDate).getMonth() + 1
            const endQuarter = Math.ceil(endMonth / 3)

            let query = Database
                .from('finance_revenue as fr')
                .leftJoin('finance_squads as s', 'fr.squad_id', 's.id')
                .where('s.status', 'active')

            // Áp dụng điều kiện thời gian dựa trên timeFrame
            if (timeFrame === 'month') {
                // Lọc theo tháng và năm
                if (startYear === endYear) {
                    query = query
                        .where('fr.year', startYear)
                        .whereBetween('fr.month', [startMonth, endMonth])
                } else {
                    query = query
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('fr.year', startYear).where('fr.month', '>=', startMonth)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('fr.year', endYear).where('fr.month', '<=', endMonth)
                            })
                        })
                }
            } else if (timeFrame === 'quarter') {
                // Lọc theo quý và năm
                if (startYear === endYear) {
                    query = query
                        .where('fr.year', startYear)
                        .whereBetween('fr.quarter', [startQuarter, endQuarter])
                } else {
                    query = query
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('fr.year', startYear).where('fr.quarter', '>=', startQuarter)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('fr.year', endYear).where('fr.quarter', '<=', endQuarter)
                            })
                        })
                }
            } else {
                // Lọc theo năm
                query = query.whereBetween('fr.year', [startYear, endYear])
            }

            const squadRevenue = await query
                .groupBy('s.id', 's.name')
                .select([
                    's.id',
                    's.name as squad_name',
                    Database.raw('COALESCE(SUM(fr.value), 0) as revenue')
                ])
                .orderBy('revenue', 'desc')

            // Tính tổng để tính phần trăm
            const totalRevenue = squadRevenue.reduce((sum, item) => sum + Number(item.revenue), 0)

            const result = squadRevenue.map(item => ({
                squad: item.squad_name || 'Chưa phân bổ',
                revenue: Number(item.revenue),
                percentage: totalRevenue > 0 ? Math.round((Number(item.revenue) / totalRevenue * 100) * 100) / 100 : 0
            }))

            // Nếu không có dữ liệu, trả về dữ liệu mẫu để tránh lỗi frontend
            if (result.length === 0 || totalRevenue === 0) {
                return response.json({
                    success: true,
                    data: [{
                        squad: 'Chưa phân bổ',
                        revenue: 0,
                        percentage: 100
                    }]
                })
            }

            return response.json({
                success: true,
                data: result
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy dữ liệu doanh thu theo Squad',
                error: error.message
            })
        }
    }

    /**
     * Lấy phân bố doanh thu theo Sản phẩm (từ Finance Project)
     */
    @Get('/api/revenue/by-product')
    public async getRevenueByProduct({ request, response }: HttpContext) {
        console.log('getRevenueByProduct');
        try {
            const { timeFrame = 'month', period = 'current', month = 6, quarter = 2, year = 2025 } = request.qs()
            console.log('request.qs():', request.qs());
            const { startDate, endDate } = this.calculateDateRange(timeFrame, period, month, quarter, year)

            console.log('startDate:', startDate, 'endDate:', endDate);

            // Tính toán tháng, quý, năm từ startDate và endDate để lọc trong bảng finance_revenue
            const startYear = new Date(startDate).getFullYear()
            const startMonth = new Date(startDate).getMonth() + 1
            const startQuarter = Math.ceil(startMonth / 3)
            const endYear = new Date(endDate).getFullYear()
            const endMonth = new Date(endDate).getMonth() + 1
            const endQuarter = Math.ceil(endMonth / 3)

            let query = Database
                .from('finance_revenue as fr')
                .leftJoin('finance_project as p', 'fr.project_id', 'p.id')

            // Áp dụng điều kiện thời gian dựa trên timeFrame
            if (timeFrame === 'month') {
                if (startYear === endYear) {
                    query = query
                        .where('fr.year', startYear)
                        .whereBetween('fr.month', [startMonth, endMonth])
                } else {
                    query = query
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('fr.year', startYear).where('fr.month', '>=', startMonth)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('fr.year', endYear).where('fr.month', '<=', endMonth)
                            })
                        })
                }
            } else if (timeFrame === 'quarter') {
                if (startYear === endYear) {
                    query = query
                        .where('fr.year', startYear)
                        .whereBetween('fr.quarter', [startQuarter, endQuarter])
                } else {
                    query = query
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('fr.year', startYear).where('fr.quarter', '>=', startQuarter)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('fr.year', endYear).where('fr.quarter', '<=', endQuarter)
                            })
                        })
                }
            } else {
                query = query.whereBetween('fr.year', [startYear, endYear])
            }

            const productRevenue = await query
                .groupBy('p.id', 'p.name')
                .select([
                    'p.name as product_name',
                    Database.raw('COALESCE(SUM(fr.value), 0) as revenue')
                ])
                .orderBy('revenue', 'desc')

            console.log('productRevenue:', productRevenue);

            // Tính tổng để tính phần trăm
            const totalRevenue = productRevenue.reduce((sum, item) => sum + Number(item.revenue), 0)

            const result = productRevenue.map(item => ({
                product: item.product_name || 'Không có dữ liệu',
                revenue: Number(item.revenue),
                percentage: totalRevenue > 0 ? Math.round((Number(item.revenue) / totalRevenue * 100) * 100) / 100 : 0
            }))

            // Nếu không có dữ liệu, trả về thông báo
            if (result.length === 0 || totalRevenue === 0) {
                return response.json({
                    success: true,
                    data: [{
                        product: 'Không có dữ liệu',
                        revenue: 0,
                        percentage: 100
                    }]
                })
            }

            return response.json({
                success: true,
                data: result
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy dữ liệu doanh thu theo sản phẩm',
                error: error.message
            })
        }
    }

    /**
     * Lấy phân bố doanh thu theo Network
     */
    @Get('/api/revenue/by-network')
    public async getRevenueByNetwork({ request, response }: HttpContext) {
        try {
            const { timeFrame = 'month', period = 'current', month = 6, quarter = 2, year = 2025 } = request.qs()
            const { startDate, endDate } = this.calculateDateRange(timeFrame, period, month, quarter, year)

            // Tính toán tháng, quý, năm từ startDate và endDate để lọc trong bảng finance_revenue
            const startYear = new Date(startDate).getFullYear()
            const startMonth = new Date(startDate).getMonth() + 1
            const startQuarter = Math.ceil(startMonth / 3)
            const endYear = new Date(endDate).getFullYear()
            const endMonth = new Date(endDate).getMonth() + 1
            const endQuarter = Math.ceil(endMonth / 3)

            let query = Database.from('finance_revenue as fr')

            // Áp dụng điều kiện thời gian dựa trên timeFrame
            if (timeFrame === 'month') {
                if (startYear === endYear) {
                    query = query
                        .where('fr.year', startYear)
                        .whereBetween('fr.month', [startMonth, endMonth])
                } else {
                    query = query
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('fr.year', startYear).where('fr.month', '>=', startMonth)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('fr.year', endYear).where('fr.month', '<=', endMonth)
                            })
                        })
                }
            } else if (timeFrame === 'quarter') {
                if (startYear === endYear) {
                    query = query
                        .where('fr.year', startYear)
                        .whereBetween('fr.quarter', [startQuarter, endQuarter])
                } else {
                    query = query
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('fr.year', startYear).where('fr.quarter', '>=', startQuarter)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('fr.year', endYear).where('fr.quarter', '<=', endQuarter)
                            })
                        })
                }
            } else {
                query = query.whereBetween('fr.year', [startYear, endYear])
            }

            const networkRevenue = await query
                .groupBy('fr.name')
                .select([
                    'fr.name as network_name',
                    Database.raw('SUM(fr.value) as revenue')
                ])
                .orderBy('revenue', 'desc')

            // Tính tổng để tính phần trăm
            const totalRevenue = networkRevenue.reduce((sum, item) => sum + Number(item.revenue), 0)

            const result = networkRevenue.map(item => ({
                network: item.network_name,
                revenue: Number(item.revenue),
                percentage: totalRevenue > 0 ? Math.round((Number(item.revenue) / totalRevenue * 100) * 100) / 100 : 0
            }))

            // Nếu không có dữ liệu, trả về thông báo
            if (result.length === 0 || totalRevenue === 0) {
                return response.json({
                    success: true,
                    data: [{
                        network: 'Không có dữ liệu',
                        revenue: 0,
                        percentage: 100
                    }]
                })
            }

            return response.json({
                success: true,
                data: result
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy dữ liệu doanh thu theo Network',
                error: error.message
            })
        }
    }

    /**
     * Lấy chi tiết doanh thu
     */
    @Get('/api/revenue/details')
    public async getRevenueDetails({ request, response }: HttpContext) {
        try {
            const {
                timeFrame = 'month',
                period = 'current',
                month = 6,
                quarter = 2,
                year = 2025,
                squad = '',
                product = '',
                network = '',
                page = 1,
                limit = 10
            } = request.qs()

            const { startDate, endDate } = this.calculateDateRange(timeFrame, period, month, quarter, year)

            // Tính toán tháng, quý, năm từ startDate và endDate để lọc trong bảng finance_revenue
            const startYear = new Date(startDate).getFullYear()
            const startMonth = new Date(startDate).getMonth() + 1
            const startQuarter = Math.ceil(startMonth / 3)
            const endYear = new Date(endDate).getFullYear()
            const endMonth = new Date(endDate).getMonth() + 1
            const endQuarter = Math.ceil(endMonth / 3)

            let query = Database
                .from('finance_revenue as fr')
                .leftJoin('finance_squads as s', 'fr.squad_id', 's.id')
                .leftJoin('finance_project as p', 'fr.project_id', 'p.id')

            // Áp dụng điều kiện thời gian
            if (timeFrame === 'month') {
                if (startYear === endYear) {
                    query = query
                        .where('fr.year', startYear)
                        .whereBetween('fr.month', [startMonth, endMonth])
                } else {
                    query = query
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('fr.year', startYear).where('fr.month', '>=', startMonth)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('fr.year', endYear).where('fr.month', '<=', endMonth)
                            })
                        })
                }
            } else if (timeFrame === 'quarter') {
                if (startYear === endYear) {
                    query = query
                        .where('fr.year', startYear)
                        .whereBetween('fr.quarter', [startQuarter, endQuarter])
                } else {
                    query = query
                        .where(function (subQuery) {
                            subQuery.where(function (innerQuery) {
                                innerQuery.where('fr.year', startYear).where('fr.quarter', '>=', startQuarter)
                            }).orWhere(function (innerQuery) {
                                innerQuery.where('fr.year', endYear).where('fr.quarter', '<=', endQuarter)
                            })
                        })
                }
            } else {
                query = query.whereBetween('fr.year', [startYear, endYear])
            }

            // Áp dụng filters
            if (squad) {
                query = query.where('s.name', 'like', `%${squad}%`)
            }
            if (product) {
                query = query.where('p.name', 'like', `%${product}%`)
            }
            if (network) {
                query = query.where('fr.name', 'like', `%${network}%`)
            }

            const details = await query
                .select([
                    's.name as squad',
                    'p.name as product',
                    'fr.name as network',
                    'fr.value as revenue',
                    Database.raw(`CONCAT(fr.year, '-', LPAD(fr.month, 2, '0'), '-01') as date`)
                ])
                .orderBy('fr.year', 'desc')
                .orderBy('fr.month', 'desc')
                .paginate(page, limit)

            return response.json({
                success: true,
                data: details.all(),
                meta: {
                    total: details.total,
                    perPage: details.perPage,
                    currentPage: details.currentPage,
                    lastPage: details.lastPage
                }
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy chi tiết doanh thu',
                error: error.message
            })
        }
    }

    /**
     * Lấy xu hướng doanh thu theo thời gian
     */
    @Get('/api/revenue/trend')
    public async getRevenueTrend({ request, response }: HttpContext) {
        try {
            const {
                fromMonth = 1,
                fromYear = 2025,
                toMonth = 6,
                toYear = 2025,
                analysisType = 'total'
            } = request.qs()

            let query = Database
                .from('finance_revenue as fr')
                .whereBetween('fr.year', [fromYear, toYear])
                .where(function (subQuery) {
                    subQuery.where(function (innerQuery) {
                        innerQuery.where('fr.year', fromYear).where('fr.month', '>=', fromMonth)
                    }).orWhere(function (innerQuery) {
                        innerQuery.where('fr.year', toYear).where('fr.month', '<=', toMonth)
                    }).orWhere(function (innerQuery) {
                        innerQuery.where('fr.year', '>', fromYear).where('fr.year', '<', toYear)
                    })
                })

            if (analysisType === 'squad') {
                query = query
                    .leftJoin('finance_squads as s', 'fr.squad_id', 's.id')
                    .groupByRaw('fr.year, fr.month, s.name')
                    .select([
                        'fr.year as year',
                        'fr.month as month',
                        's.name as category',
                        Database.raw('SUM(fr.value) as revenue')
                    ])
            } else if (analysisType === 'network') {
                query = query
                    .groupByRaw('fr.year, fr.month, fr.name')
                    .select([
                        'fr.year as year',
                        'fr.month as month',
                        'fr.name as category',
                        Database.raw('SUM(fr.value) as revenue')
                    ])
            } else if (analysisType === 'product') {
                query = query
                    .leftJoin('finance_project as p', 'fr.project_id', 'p.id')
                    .groupByRaw('fr.year, fr.month, p.name')
                    .select([
                        'fr.year as year',
                        'fr.month as month',
                        'p.name as category',
                        Database.raw('SUM(fr.value) as revenue')
                    ])
            } else {
                // Total revenue
                query = query
                    .groupByRaw('fr.year, fr.month')
                    .select([
                        'fr.year as year',
                        'fr.month as month',
                        Database.raw('SUM(fr.value) as revenue')
                    ])
            }

            const trendData = await query.orderBy('year').orderBy('month')

            return response.json({
                success: true,
                data: trendData
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy xu hướng doanh thu',
                error: error.message
            })
        }
    }

    /**
     * Helper method để tính toán khoảng thời gian
     */
    private calculateDateRange(timeFrame: string, period: string, month: number, quarter: number, year: number) {
        let startDate: string, endDate: string, prevStartDate: string, prevEndDate: string

        if (timeFrame === 'month') {
            const currentMonth = period === 'current' ? month : (month === 1 ? 12 : month - 1)
            const currentYear = period === 'current' ? year : (month === 1 ? year - 1 : year)

            // Lấy ngày cuối cùng của tháng hiện tại
            const lastDay = new Date(currentYear, currentMonth, 0).getDate();
            startDate = `${currentYear}-${String(currentMonth).padStart(2, '0')}-01`
            endDate = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${lastDay}`

            const prevMonth = currentMonth === 1 ? 12 : currentMonth - 1
            const prevYear = currentMonth === 1 ? currentYear - 1 : currentYear
            const prevLastDay = new Date(prevYear, prevMonth, 0).getDate();
            prevStartDate = `${prevYear}-${String(prevMonth).padStart(2, '0')}-01`
            prevEndDate = `${prevYear}-${String(prevMonth).padStart(2, '0')}-${prevLastDay}`

        } else if (timeFrame === 'quarter') {
            const currentQuarter = period === 'current' ? quarter : (quarter === 1 ? 4 : quarter - 1)
            const currentYear = period === 'current' ? year : (quarter === 1 ? year - 1 : year)

            const quarterStartMonth = (currentQuarter - 1) * 3 + 1
            const quarterEndMonth = currentQuarter * 3
            const lastDay = new Date(currentYear, quarterEndMonth, 0).getDate();

            startDate = `${currentYear}-${String(quarterStartMonth).padStart(2, '0')}-01`
            endDate = `${currentYear}-${String(quarterEndMonth).padStart(2, '0')}-${lastDay}`

            const prevQuarter = currentQuarter === 1 ? 4 : currentQuarter - 1
            const prevYear = currentQuarter === 1 ? currentYear - 1 : currentYear
            const prevQuarterStartMonth = (prevQuarter - 1) * 3 + 1
            const prevQuarterEndMonth = prevQuarter * 3
            const prevLastDay = new Date(prevYear, prevQuarterEndMonth, 0).getDate();

            prevStartDate = `${prevYear}-${String(prevQuarterStartMonth).padStart(2, '0')}-01`
            prevEndDate = `${prevYear}-${String(prevQuarterEndMonth).padStart(2, '0')}-${prevLastDay}`

        } else { // year
            const currentYear = period === 'current' ? year : year - 1

            startDate = `${currentYear}-01-01`
            endDate = `${currentYear}-12-31`

            prevStartDate = `${currentYear - 1}-01-01`
            prevEndDate = `${currentYear - 1}-12-31`
        }

        return { startDate, endDate, prevStartDate, prevEndDate }
    }
}
