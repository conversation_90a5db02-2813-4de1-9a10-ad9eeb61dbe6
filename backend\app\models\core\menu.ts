import { BaseModel, column, beforeSave } from '@adonisjs/lucid/orm'
import Helper from '#libraries/vcms/helper'
import ColumnData from '#models/core/types/column_data'
import MenuItem from './menu_item.js'

export default class Menu extends BaseModel {
    public static table = 'menus'
    public static columns: Record<string, ColumnData> = {
        id: {
            type: 'int',
            length: 11,
            required: true,
            column: 12,
            listing: true,
        },
        name: {
            type: 'varchar',
            length: 50,
            required: true,
            column: 12,
            listing: true,
        },
        description: {
            type: 'text',
            length: 65535,
            required: false,
            column: 12,
        },
        prefix: {
            type: 'varchar',
            length: 50,
            required: false,
            column: 12,
            listing: true,
        },
        status: {
            type: 'int',
            length: 11,
            required: false,
            column: 12,
            listing: {
                type: 'status',
                enum: {
                    0: 'Inactived',
                    1: 'Actived',
                },
            },
        },
        type: {
            type: 'int',
            length: 11,
            required: false,
            column: 12,
            listing: true,
        },
        created: {
            type: 'int',
            length: 11,
            required: false,
            column: 12,
            listing: {
                class: 'text-end',
                type: 'date',
                sortable: true,
            },
            invisible: true,
        },
        updated: {
            type: 'int',
            length: 11,
            required: false,
            column: 12,
            invisible: true,
        },
    }

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare name: string

    @column()
    declare description: string

    @column()
    declare prefix: string

    @column()
    declare status: number

    @column()
    declare type: number

    @column()
    declare created: number

    @column()
    declare updated: number

    @beforeSave()
    public static async autoTimestamp(data: Menu) {
        if (!data.created) {
            data.created = Helper.now_timestamp()
        }
        data.updated = Helper.now_timestamp()
    }

    public static async get_menu(menu_id: number, parrent_id = 0) {
        var menu_data: any = await MenuItem.query().where('menu_id', menu_id).where('parrent_id', parrent_id).orderBy('order')
        for (let index in menu_data) {
            var item: any = menu_data[index].toJSON()
            let sub_menu = await this.get_menu(menu_id, item?.id)
            if (sub_menu.length > 0) {
                item.sub_menu = sub_menu
            }
            menu_data[index] = item
        }
        return menu_data
    }
}
