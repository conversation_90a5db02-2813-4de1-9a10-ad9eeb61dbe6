import { BaseModel, column, beforeSave } from '@adonisjs/lucid/orm'
import Helper from '#libraries/vcms/helper'

export default class FinanceRevenue extends BaseModel {
    public static table = 'finance_revenue'

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare project_id: number | null

    @column()
    declare squad_id: number | null

    @column()
    declare network_id: string

    @column()
    declare type: number

    @column()
    declare month: number

    @column()
    declare quarter: number

    @column()
    declare year: number

    @column()
    declare name: string

    @column()
    declare value: number

    @column()
    declare updated: number

    @column()
    declare created: number

    @beforeSave()
    public static async autoTimestamp(data: FinanceRevenue) {
        if (!data.created) {
            data.created = Helper.now_timestamp()
        }
        data.updated = Helper.now_timestamp()
    }
}