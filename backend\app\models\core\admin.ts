import hash from '@adonisjs/core/services/hash'
import { compose } from '@adonisjs/core/helpers'
import { BaseModel, column, beforeSave, hasOne } from '@adonisjs/lucid/orm'
import { DbRememberMeTokensProvider } from '@adonisjs/auth/session'
import type { HasOne } from '@adonisjs/lucid/types/relations'
import Helper from '#libraries/vcms/helper'
import ColumnData from '#models/core/types/column_data'
import { withAuthFinder } from '@adonisjs/auth'
import Role from './role.js'
import { MorphMap } from '@holoyan/adonisjs-permissions'
import { AclModelInterface } from '@holoyan/adonisjs-permissions/types'
import { hasPermissions } from '@holoyan/adonisjs-permissions'

const AuthFinder = withAuthFinder(() => hash.use('bcrypt'), {
    uids: ['username', 'email'],
    passwordColumnName: 'password',
})

@MorphMap('admins')
export default class Admin extends compose(BaseModel, AuthFinder, hasPermissions()) implements AclModelInterface {
    getModelId(): number {
        return this.id
    }
    public static table = 'admins'
    public static uniques = ['username', 'email', 'token']
    public static columns: Record<string, ColumnData> = {
        'id': {
            type: 'int',
            length: 10,
            required: true,
            label: 'ID',
            column: 6,
            listing: {
                sortable: true,
            },
        },
        'name': {
            type: 'varchar',
            length: 255,
            required: false,
            column: 6,
            listing: true,
        },
        'username': {
            type: 'varchar',
            length: 24,
            required: true,
            column: 6,
            listing: {
                sortable: true,
            },
        },
        'email': {
            type: 'varchar',
            length: 100,
            required: true,
            column: 6,
            listing: true,
        },
        'password': {
            type: 'varchar',
            length: 255,
            required: false,
            column: 6,
        },
        'salt': {
            type: 'varchar',
            length: 32,
            required: false,
            invisible: true,
        },
        'role_id': {
            type: 'int',
            length: 11,
            required: true,
            column: 3,
            relation: {
                key: 'role',
                show_all: true,
                template: '<span style="color: {color}; font-weight: bold">{name}</span>',
            },
        },
        'role.name': {
            label: 'ROLE',
            invisible: true,
            listing: {
                template: '<span style="color: {role.color}; font-weight: bold">{role.name}</span>',
            },
        },
        'tele_token': {
            type: 'varchar',
            length: 255,
            required: false,
            column: 6,
        },
        'tele_chatid': {
            type: 'varchar',
            length: 255,
            required: false,
            column: 3,
        },
        'avatar': {
            type: 'varchar',
            length: 100,
            required: false,
            column: 6,
        },
        'ip_address': {
            type: 'varchar',
            length: 15,
            required: false,
            column: 6,
        },
        'activated': {
            type: 'int',
            length: 11,
            required: false,
            column: 6,
        },
        'status': {
            type: 'int',
            length: 11,
            required: false,
            column: 3,
            listing: {
                type: 'status',
                enum: {
                    0: 'Inactived',
                    1: 'Actived',
                },
            },
        },
        'token': {
            type: 'varchar',
            length: 255,
            required: false,
            invisible: true,
        },
        'token_by': {
            type: 'varchar',
            length: 100,
            required: false,
            invisible: true,
        },
        'rememberMeToken': {
            type: 'varchar',
            length: 255,
            required: false,
            invisible: true,
        },
        'auth_secret': {
            type: 'varchar',
            length: 32,
            required: false,
            invisible: true,
        },
        'auth_status': {
            type: 'int',
            length: 11,
            required: false,
            column: 3,
        },
        'created': {
            type: 'int',
            length: 11,
            required: false,
            invisible: true,
            listing: {
                class: 'text-end',
                type: 'date',
                sortable: true,
            },
        },
        'updated': {
            type: 'int',
            length: 11,
            required: false,
            invisible: true,
        },
    }

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare name: string

    @column()
    declare username: string

    @column()
    declare email: string

    @column()
    declare password: string

    @column()
    declare salt: string

    @column()
    declare role_id: number

    @column()
    declare revenue: string

    @column()
    declare tele_token: string

    @column()
    declare tele_chatid: string

    @column()
    declare avatar: string

    @column()
    declare ip_address: string

    @column()
    declare activated: number

    @column()
    declare status: number

    @column()
    declare token: string

    @column()
    declare token_by: string

    @column()
    declare auth_secret: string

    @column()
    declare auth_status: number

    @column()
    declare created: number

    @column()
    declare updated: number

    @column()
    declare deleted: number

    static rememberMeTokens = DbRememberMeTokensProvider.forModel(Admin)

    @beforeSave()
    public static async autoTimestamp(data: Admin) {
        if (!data.created) {
            data.created = Helper.now_timestamp()
        }
        data.updated = Helper.now_timestamp()
    }

    // @beforeSave()
    // public static async hashPassword(admin: Admin) {
    //     if (admin.$dirty.password) {
    //         console.log('admin', admin.password)
    //         admin.password = await hash.use('bcrypt').make(admin.password)
    //     } else {
    //         if (admin.password == '') {
    //             admin.password = admin.$original.password
    //         }
    //     }
    // }

    @hasOne(() => Role, {
        localKey: 'role_id',
        foreignKey: 'id',
    })
    declare role: HasOne<typeof Role>
}
