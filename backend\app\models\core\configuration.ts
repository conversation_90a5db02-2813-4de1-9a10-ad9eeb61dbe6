import { BaseModel, column, hasOne } from '@adonisjs/lucid/orm'
import ColumnData from '#models/core/types/column_data'
import VLogger from '#libraries/vcms/vlogger'
import ConfigurationGroup from '#models/core/configuration_group'
import type { HasOne } from '@adonisjs/lucid/types/relations'

export default class Configuration extends BaseModel {
    public static table = 'configurations'
    public static uniques = ['code']
    public static columns: Record<string, ColumnData> = {
        id: {
            type: 'int',
            length: 11,
            required: true,
        },
        name: {
            type: 'varchar',
            length: 50,
            required: false,
        },
        code: {
            type: 'varchar',
            length: 50,
            required: true,
        },
        value: {
            type: 'text',
            length: 65535,
            required: false,
        },
        description: {
            type: 'varchar',
            length: 100,
            required: false,
        },
        type: {
            type: 'enum',
            length: 9,
            required: false,
        },
        group_id: {
            type: 'int',
            length: 11,
            required: true,
        },
        extra_data: {
            type: 'varchar',
            length: 255,
            required: false,
        },
        order: {
            type: 'int',
            length: 11,
            required: false,
        },
    }

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare name: string

    @column()
    declare code: string

    @column()
    declare value: string

    @column()
    declare description: string

    @column()
    declare type: string

    @column()
    declare group_id: number

    @column()
    declare extra_data: string

    @column()
    declare order: number

    @hasOne(() => ConfigurationGroup, {
        localKey: 'group_id',
        foreignKey: 'id',
    })
    declare group: HasOne<typeof ConfigurationGroup>

    static async getByValue(code: string, default_value: any = undefined) {
        try {
            let data: any = await this.query().where('code', code).first()
            if (typeof data?.value != 'undefined') {
                return data.value
            }
        } catch (e) {
            VLogger.error(e)
        }
        return default_value
    }
    static async update(code: string, value: string) {
        try {
            // await Cache.forget(code)
            let data: any = await this.query().where('code', code).first()
            if (data?.id) {
                data.value = value
                await data.save()
                return true
            } else {
                data = new Configuration()
                data.code = code
                data.value = value
                data.group = 0
                data.order = 0
                await data.save()
                return true
            }
        } catch (e) {
            VLogger.error(e)
        }
        return false
    }
}
