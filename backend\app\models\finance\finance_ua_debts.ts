import { BaseModel, column, beforeSave } from '@adonisjs/lucid/orm'
import Helper from '#libraries/vcms/helper'

export default class FinanceUaDebts extends BaseModel {
    public static table = 'finance_ua_debts'

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare account: string

    @column()
    declare network_id: number

    @column()
    declare team: string | null

    @column()
    declare network: string | null

    @column()
    declare network_code: string | null

    @column()
    declare value: number

    @column()
    declare currency: string | null

    @column()
    declare fee_bank: number

    @column()
    declare content_transfer: string | null

    @column()
    declare swift_code: string | null

    @column()
    declare payment_number: string | null

    @column()
    declare payment_name: string | null

    @column()
    declare status: number

    @column()
    declare payment_date: number

    @column()
    declare due_date: number

    @column()
    declare note: string | null

    @column()
    declare month: number

    @column()
    declare quarter: number

    @column()
    declare year: number

    @column()
    declare updated: number

    @column()
    declare created: number

    @beforeSave()
    public static async autoTimestamp(data: FinanceUaDebts) {
        if (!data.created) {
            data.created = Helper.now_timestamp()
        }
        data.updated = Helper.now_timestamp()
    }
} 