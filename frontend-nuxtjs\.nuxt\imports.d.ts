export { isVue2, isVue3 } from 'vue-demi';
export { defineNuxtLink } from '#app/components/nuxt-link';
export { useNuxtApp, tryUseNuxtApp, defineNuxtPlugin, definePayloadPlugin, useRuntimeConfig, defineAppConfig } from '#app/nuxt';
export { useAppConfig, updateAppConfig } from '#app/config';
export { defineNuxtComponent } from '#app/composables/component';
export { useAsyncData, useLazyAsyncData, useNuxtData, refreshNuxtData, clearNuxtData } from '#app/composables/asyncData';
export { useHydration } from '#app/composables/hydrate';
export { callOnce } from '#app/composables/once';
export { useState, clearNuxtState } from '#app/composables/state';
export { clearError, createError, isNuxtError, showError, useError } from '#app/composables/error';
export { useFetch, useLazyFetch } from '#app/composables/fetch';
export { useCookie, refreshCookie } from '#app/composables/cookie';
export { onPrehydrate, prerenderRoutes, useRequestHeader, useRequestHeaders, useResponseHeader, useRequestEvent, useRequestFetch, setResponseStatus } from '#app/composables/ssr';
export { onNuxtReady } from '#app/composables/ready';
export { preloadComponents, prefetchComponents, preloadRouteComponents } from '#app/composables/preload';
export { abortNavigation, addRouteMiddleware, defineNuxtRouteMiddleware, setPageLayout, navigateTo, useRoute, useRouter } from '#app/composables/router';
export { isPrerendered, loadPayload, preloadPayload, definePayloadReducer, definePayloadReviver } from '#app/composables/payload';
export { useLoadingIndicator } from '#app/composables/loading-indicator';
export { getAppManifest, getRouteRules } from '#app/composables/manifest';
export { reloadNuxtApp } from '#app/composables/chunk';
export { useRequestURL } from '#app/composables/url';
export { usePreviewMode } from '#app/composables/preview';
export { useRouteAnnouncer } from '#app/composables/route-announcer';
export { useRuntimeHook } from '#app/composables/runtime-hook';
export { useHead, useHeadSafe, useServerHeadSafe, useServerHead, useSeoMeta, useServerSeoMeta, injectHead } from '#app/composables/head';
export { onBeforeRouteLeave, onBeforeRouteUpdate, useLink } from 'vue-router';
export { withCtx, withDirectives, withKeys, withMemo, withModifiers, withScopeId, onActivated, onBeforeMount, onBeforeUnmount, onBeforeUpdate, onDeactivated, onErrorCaptured, onMounted, onRenderTracked, onRenderTriggered, onServerPrefetch, onUnmounted, onUpdated, computed, customRef, isProxy, isReactive, isReadonly, isRef, markRaw, proxyRefs, reactive, readonly, ref, shallowReactive, shallowReadonly, shallowRef, toRaw, toRef, toRefs, triggerRef, unref, watch, watchEffect, watchPostEffect, watchSyncEffect, isShallow, effect, effectScope, getCurrentScope, onScopeDispose, defineComponent, defineAsyncComponent, resolveComponent, getCurrentInstance, h, inject, hasInjectionContext, nextTick, provide, mergeModels, toValue, useModel, useAttrs, useCssModule, useCssVars, useSlots, useTransitionState, useId, useTemplateRef, useShadowRoot, Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue';
export { requestIdleCallback, cancelIdleCallback } from '#app/compat/idle-callback';
export { setInterval } from '#app/compat/interval';
export { useApi, ApiResponse, PaginatedResponse } from '../composables/useApi';
export { useAuth } from '../composables/useAuth';
export { useCharts } from '../composables/useCharts';
export { formatVND, formatUSD, formatNumber, formatPercentage, formatDate, formatShortNumber, getStatusColor, getROIColor } from '../utils/formatters';
export { useDashboardStore } from '../stores/dashboard';
export { useScript } from '../node_modules/@nuxt/scripts/dist/runtime/composables/useScript';
export { useScriptEventPage } from '../node_modules/@nuxt/scripts/dist/runtime/composables/useScriptEventPage';
export { useScriptTriggerConsent } from '../node_modules/@nuxt/scripts/dist/runtime/composables/useScriptTriggerConsent';
export { useScriptTriggerElement } from '../node_modules/@nuxt/scripts/dist/runtime/composables/useScriptTriggerElement';
export { useScriptPlausibleAnalytics } from '../node_modules/@nuxt/scripts/dist/runtime/registry/plausible-analytics';
export { useScriptCloudflareWebAnalytics } from '../node_modules/@nuxt/scripts/dist/runtime/registry/cloudflare-web-analytics';
export { useScriptFathomAnalytics } from '../node_modules/@nuxt/scripts/dist/runtime/registry/fathom-analytics';
export { useScriptMatomoAnalytics } from '../node_modules/@nuxt/scripts/dist/runtime/registry/matomo-analytics';
export { useScriptRybbitAnalytics } from '../node_modules/@nuxt/scripts/dist/runtime/registry/rybbit-analytics';
export { useScriptSegment } from '../node_modules/@nuxt/scripts/dist/runtime/registry/segment';
export { useScriptMetaPixel } from '../node_modules/@nuxt/scripts/dist/runtime/registry/meta-pixel';
export { useScriptXPixel } from '../node_modules/@nuxt/scripts/dist/runtime/registry/x-pixel';
export { useScriptSnapchatPixel } from '../node_modules/@nuxt/scripts/dist/runtime/registry/snapchat-pixel';
export { useScriptGoogleAdsense } from '../node_modules/@nuxt/scripts/dist/runtime/registry/google-adsense';
export { useScriptIntercom } from '../node_modules/@nuxt/scripts/dist/runtime/registry/intercom';
export { useScriptHotjar } from '../node_modules/@nuxt/scripts/dist/runtime/registry/hotjar';
export { useScriptClarity } from '../node_modules/@nuxt/scripts/dist/runtime/registry/clarity';
export { useScriptStripe } from '../node_modules/@nuxt/scripts/dist/runtime/registry/stripe';
export { useScriptLemonSqueezy } from '../node_modules/@nuxt/scripts/dist/runtime/registry/lemon-squeezy';
export { useScriptVimeoPlayer } from '../node_modules/@nuxt/scripts/dist/runtime/registry/vimeo-player';
export { useScriptYouTubePlayer } from '../node_modules/@nuxt/scripts/dist/runtime/registry/youtube-player';
export { useScriptGoogleMaps } from '../node_modules/@nuxt/scripts/dist/runtime/registry/google-maps';
export { useScriptCrisp } from '../node_modules/@nuxt/scripts/dist/runtime/registry/crisp';
export { useScriptNpm } from '../node_modules/@nuxt/scripts/dist/runtime/registry/npm';
export { useScriptGoogleTagManager } from '../node_modules/@nuxt/scripts/dist/runtime/registry/google-tag-manager';
export { useScriptGoogleAnalytics } from '../node_modules/@nuxt/scripts/dist/runtime/registry/google-analytics';
export { useScriptUmamiAnalytics } from '../node_modules/@nuxt/scripts/dist/runtime/registry/umami-analytics';
export { defineStore, acceptHMRUpdate, usePinia, storeToRefs } from '../node_modules/@pinia/nuxt/dist/runtime/composables';
export { definePageMeta } from '../node_modules/nuxt/dist/pages/runtime/composables';