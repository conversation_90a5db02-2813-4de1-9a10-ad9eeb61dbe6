// Generated by nuxi
{
  "compilerOptions": {
    "paths": {
      "nitropack/types": [
        "../node_modules/nitropack/types"
      ],
      "nitropack/runtime": [
        "../node_modules/nitropack/runtime"
      ],
      "nitropack": [
        "../node_modules/nitropack"
      ],
      "defu": [
        "../node_modules/defu"
      ],
      "h3": [
        "../node_modules/h3"
      ],
      "consola": [
        "../node_modules/consola"
      ],
      "ofetch": [
        "../node_modules/ofetch"
      ],
      "@nuxt/devtools": [
        "../node_modules/@nuxt/devtools"
      ],
      "@vue/runtime-core": [
        "../node_modules/@vue/runtime-core"
      ],
      "@vue/compiler-sfc": [
        "../node_modules/@vue/compiler-sfc"
      ],
      "unplugin-vue-router/client": [
        "../node_modules/unplugin-vue-router/client"
      ],
      "@nuxt/schema": [
        "../node_modules/@nuxt/schema"
      ],
      "nuxt": [
        "../node_modules/nuxt"
      ],
      "vite/client": [
        "../node_modules/vite/client"
      ],
      "~": [
        ".."
      ],
      "~/*": [
        "../*"
      ],
      "@": [
        ".."
      ],
      "@/*": [
        "../*"
      ],
      "~~": [
        ".."
      ],
      "~~/*": [
        "../*"
      ],
      "@@": [
        ".."
      ],
      "@@/*": [
        "../*"
      ],
      "#shared": [
        "../shared"
      ],
      "assets": [
        "../assets"
      ],
      "public": [
        "../public"
      ],
      "public/*": [
        "../public/*"
      ],
      "#app": [
        "../node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "../node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "../node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "#nuxt-scripts-validator": [
        "../node_modules/@nuxt/scripts/dist/runtime/validation/valibot"
      ],
      "#nuxt-scripts": [
        "../node_modules/@nuxt/scripts/dist/runtime"
      ],
      "#nuxt-scripts/*": [
        "../node_modules/@nuxt/scripts/dist/runtime/*"
      ],
      "#vue-router": [
        "../node_modules/vue-router"
      ],
      "#unhead/composables": [
        "../node_modules/nuxt/dist/head/runtime/composables/v3"
      ],
      "#imports": [
        "./imports"
      ],
      "#app-manifest": [
        "./manifest/meta/dev"
      ],
      "#components": [
        "./components"
      ],
      "#build": [
        "."
      ],
      "#build/*": [
        "./*"
      ]
    },
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "ESNext",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    "strict": true,
    "noUncheckedIndexedAccess": false,
    "forceConsistentCasingInFileNames": true,
    "noImplicitOverride": true,
    "module": "preserve",
    "noEmit": true,
    "lib": [
      "ESNext",
      "dom",
      "dom.iterable",
      "webworker"
    ],
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "types": [],
    "moduleResolution": "Bundler",
    "useDefineForClassFields": true,
    "noImplicitThis": true,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "../**/*",
    "../.config/nuxt.*",
    "./nuxt.d.ts",
    "../node_modules/@nuxt/eslint/runtime",
    "../node_modules/@nuxt/eslint/dist/runtime",
    "../node_modules/@nuxt/fonts/runtime",
    "../node_modules/@nuxt/fonts/dist/runtime",
    "../node_modules/@nuxt/scripts/runtime",
    "../node_modules/@nuxt/scripts/dist/runtime",
    "../node_modules/@pinia/nuxt/runtime",
    "../node_modules/@pinia/nuxt/dist/runtime",
    "../node_modules/@nuxt/telemetry/runtime",
    "../node_modules/@nuxt/telemetry/dist/runtime",
    ".."
  ],
  "exclude": [
    "../dist",
    "../.data",
    "../node_modules",
    "../../node_modules",
    "../node_modules/nuxt/node_modules",
    "../node_modules/@nuxt/eslint/node_modules",
    "../node_modules/@nuxt/fonts/node_modules",
    "../node_modules/@nuxt/scripts/node_modules",
    "../node_modules/@pinia/nuxt/node_modules",
    "../node_modules/@nuxt/telemetry/node_modules",
    "../node_modules/@nuxt/eslint/runtime/server",
    "../node_modules/@nuxt/eslint/dist/runtime/server",
    "../node_modules/@nuxt/fonts/runtime/server",
    "../node_modules/@nuxt/fonts/dist/runtime/server",
    "../node_modules/@nuxt/scripts/runtime/server",
    "../node_modules/@nuxt/scripts/dist/runtime/server",
    "../node_modules/@pinia/nuxt/runtime/server",
    "../node_modules/@pinia/nuxt/dist/runtime/server",
    "../node_modules/@nuxt/telemetry/runtime/server",
    "../node_modules/@nuxt/telemetry/dist/runtime/server",
    "../.output"
  ]
}