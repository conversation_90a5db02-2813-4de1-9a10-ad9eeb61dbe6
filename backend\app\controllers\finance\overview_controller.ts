// import type { HttpContext } from '@adonisjs/core/http'

import { Get, ApiOnly, Prefix } from '#libraries/vcms/route_decorators'
import { HttpContext } from '@adonisjs/core/http'
import Database from '@adonisjs/lucid/services/db'
import FinanceCurrenciesMonth from '#models/finance/finance_currencies_month'

@ApiOnly()
@Prefix('/api/overview')
export default class OverviewController {

    @Get('/get_balance_bank_accounts')
    public async getBalanceBankAccounts({ response }: HttpContext) {
        try {
            // Lấy tổng số dư tất cả tài khoản (chỉ tài khoản active)
            const totalBalanceResult = await Database
                .from('finance_bank_accounts')
                .where('status', 1)
                .sum('current_balance as total')
                .first()

            // Lấy số dư theo từng loại tài khoản
            const balancesByType = await Database
                .from('finance_bank_accounts')
                .select('account_type', 'account_name')
                .sum('current_balance as balance')
                .where('status', 1)
                .groupBy('account_type', 'account_name')
                .orderBy('balance', 'desc')

            // Định nghĩa màu sắc cho từng loại tài khoản
            const accountTypeColors: Record<string, string> = {
                'PO_HK': '#fa8c16',
                'PO_BVI': '#52c41a',
                'OCBC_BVI': '#eb2f96',
            }

            // Tạo danh sách động dựa trên dữ liệu thực tế
            const accountTypes = balancesByType.map(item => {
                const balance = Math.round((item.balance || 0) * 100) / 100
                return {
                    account_type: item.account_type,
                    account_name: item.account_name || item.account_type,
                    balance: balance,
                    color: accountTypeColors[item.account_type] || '#666666'
                }
            })

            // Lấy thông tin cập nhật mới nhất
            const latestUpdate = await Database
                .from('finance_bank_accounts')
                .where('status', 1)
                .orderBy('last_balance_update', 'desc')
                .first()

            return response.json({
                success: true,
                data: {
                    totalBalance: Math.round((totalBalanceResult?.total || 0) * 100) / 100,
                    accountTypes: accountTypes,
                    currency: 'USD',
                    last_updated_timestamp: latestUpdate?.last_balance_update || 0,
                    last_updated_date: latestUpdate ?
                        new Date(latestUpdate.last_balance_update * 1000).toLocaleDateString('vi-VN') : null
                }
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy dữ liệu số dư tài khoản ngân hàng',
                error: error.message
            })
        }
    }

    @Get('/get_debts_overview')
    public async getDebtsOverview({ request, response }: HttpContext) {
        try {
            const {
                timeFrame = 'month',
                period = 'current',
                selectedMonth,
                selectedYear,
                selectedQuarter
            } = request.qs()

            // Tính toán khoảng thời gian dựa trên parameters
            const { currentPeriod } = this.calculatePeriods(timeFrame, period, selectedMonth, selectedYear, selectedQuarter)
            const currentTime = Math.floor(Date.now() / 1000)

            // === MONETIZATION DEBTS ===
            // Tổng công nợ Monetization chờ thanh toán (amount_real = 0)
            const monetizationPendingQuery = Database.from('finance_monetization_debts')
                .sum('amount as total')
                .where('amount_real', 0)

            this.addPeriodFilter(monetizationPendingQuery, currentPeriod, timeFrame)

            const monetizationPendingResult = await monetizationPendingQuery.first()
            const totalMonetizationDebt = monetizationPendingResult?.total || 0

            // Công nợ Monetization quá hạn (amount_real = 0 và expected_date < hiện tại)
            const monetizationOverdueQuery = Database.from('finance_monetization_debts')
                .sum('amount as total')
                .where('amount_real', 0)
                .where('expected_date', '<', currentTime)

            this.addPeriodFilter(monetizationOverdueQuery, currentPeriod, timeFrame)

            const monetizationOverdueResult = await monetizationOverdueQuery.first()
            const overdueMonetizationDebt = monetizationOverdueResult?.total || 0

            // === UA DEBTS ===
            // Tổng công nợ UA chờ thanh toán (status = 0 - Pending)
            const uaPendingQuery = Database.from('finance_ua_debts')
                .select(['value', 'currency', 'month', 'year'])
                .where('status', -1)

            // Tạm thời comment filter để test
            this.addPeriodFilter(uaPendingQuery, currentPeriod, timeFrame)

            console.log('UA Pending Query SQL:', uaPendingQuery.toSQL())
            const uaPendingResult = await uaPendingQuery
            console.log('UA Pending Result:', uaPendingResult)
            const totalUADebt = await this.convertToUSD(uaPendingResult)

            // Công nợ UA quá hạn (status = 0 và due_date < hiện tại)
            const uaOverdueQuery = Database.from('finance_ua_debts')
                .select(['value', 'currency', 'month', 'year'])
                .where('status', -1)
                .where('due_date', '<', currentTime)

            this.addPeriodFilter(uaOverdueQuery, currentPeriod, timeFrame)

            const uaOverdueResult = await uaOverdueQuery
            const overdueUADebt = await this.convertToUSD(uaOverdueResult)

            return response.json({
                success: true,
                data: {
                    monetization: {
                        totalDebt: Math.round(totalMonetizationDebt * 100) / 100,
                        overdueDebt: Math.round(overdueMonetizationDebt * 100) / 100
                    },
                    ua: {
                        totalDebt: Math.round(totalUADebt * 100) / 100,
                        overdueDebt: Math.round(overdueUADebt * 100) / 100
                    },
                    combined: {
                        totalDebt: Math.round((totalMonetizationDebt + totalUADebt) * 100) / 100,
                        overdueDebt: Math.round((overdueMonetizationDebt + overdueUADebt) * 100) / 100
                    },
                    timeFrame,
                    period,
                    currentPeriod
                }
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy tổng quan công nợ',
                error: error.message
            })
        }
    }

    @Get('/get_financial_overview')
    public async getFinancialOverview({ request, response }: HttpContext) {
        try {
            const {
                timeFrame = 'month',
                period = 'current',
                selectedMonth,
                selectedYear,
                selectedQuarter
            } = request.qs()

            // Tính toán khoảng thời gian hiện tại và kỳ trước
            const { currentPeriod, previousPeriod } = this.calculatePeriodsForFinancialOverview(
                timeFrame,
                period,
                selectedMonth,
                selectedYear,
                selectedQuarter
            )

            // === TỔNG DOANH THU (từ bảng finance_monetization_debts) ===

            // Doanh thu hiện tại
            const currentRevenueQuery = Database.from('finance_monetization_debts')
                .sum('amount as total')
                .whereNotNull('amount')

            this.addPeriodFilter(currentRevenueQuery, currentPeriod, timeFrame)
            const currentRevenueResult = await currentRevenueQuery.first()
            const currentRevenue = currentRevenueResult?.total || 0

            // Doanh thu kỳ trước
            const previousRevenueQuery = Database.from('finance_monetization_debts')
                .sum('amount as total')
                .whereNotNull('amount')

            this.addPeriodFilter(previousRevenueQuery, previousPeriod, timeFrame)
            const previousRevenueResult = await previousRevenueQuery.first()
            const previousRevenue = previousRevenueResult?.total || 0

            // Tính % thay đổi doanh thu
            const revenueChange = previousRevenue > 0
                ? ((currentRevenue - previousRevenue) / previousRevenue * 100)
                : 0

            // === TỔNG CHI PHÍ (từ bảng finance_ua_debts) ===

            // Chi phí hiện tại
            const currentExpenseQuery = Database.from('finance_ua_debts')
                .select(['value', 'currency', 'month', 'year'])

            this.addPeriodFilter(currentExpenseQuery, currentPeriod, timeFrame)
            const currentExpenseResult = await currentExpenseQuery
            const currentExpenses = await this.convertToUSD(currentExpenseResult)

            // Chi phí kỳ trước
            const previousExpenseQuery = Database.from('finance_ua_debts')
                .select(['value', 'currency', 'month', 'year'])

            this.addPeriodFilter(previousExpenseQuery, previousPeriod, timeFrame)
            const previousExpenseResult = await previousExpenseQuery
            const previousExpenses = await this.convertToUSD(previousExpenseResult)

            // Tính % thay đổi chi phí
            const expensesChange = previousExpenses > 0
                ? ((currentExpenses - previousExpenses) / previousExpenses * 100)
                : 0

            // === TÍNH TOÁN LỢI NHUẬN VÀ TỶ SUẤT ===

            const currentProfit = currentRevenue - currentExpenses
            const previousProfit = previousRevenue - previousExpenses

            // Tính % thay đổi lợi nhuận
            const profitChange = previousProfit !== 0
                ? ((currentProfit - previousProfit) / Math.abs(previousProfit) * 100)
                : 0

            // Tính tỷ suất lợi nhuận
            const currentProfitMargin = currentRevenue > 0
                ? (currentProfit / currentRevenue * 100)
                : 0

            const previousProfitMargin = previousRevenue > 0
                ? (previousProfit / previousRevenue * 100)
                : 0

            // Tính thay đổi tỷ suất lợi nhuận (điểm phần trăm)
            const marginChange = currentProfitMargin - previousProfitMargin

            return response.json({
                success: true,
                data: {
                    // Số liệu hiện tại
                    totalRevenue: Math.round(currentRevenue * 100) / 100,
                    totalExpenses: Math.round(currentExpenses * 100) / 100,
                    totalProfit: Math.round(currentProfit * 100) / 100,
                    profitMargin: Math.round(currentProfitMargin * 100) / 100,

                    // Số liệu kỳ trước
                    previousRevenue: Math.round(previousRevenue * 100) / 100,
                    previousExpenses: Math.round(previousExpenses * 100) / 100,
                    previousProfit: Math.round(previousProfit * 100) / 100,
                    previousProfitMargin: Math.round(previousProfitMargin * 100) / 100,

                    // % thay đổi so với kỳ trước
                    revenueChange: Math.round(revenueChange * 100) / 100,
                    expensesChange: Math.round(expensesChange * 100) / 100,
                    profitChange: Math.round(profitChange * 100) / 100,
                    marginChange: Math.round(marginChange * 100) / 100,

                    // Metadata
                    timeFrame,
                    period,
                    currentPeriod,
                    previousPeriod,
                    currency: 'USD'
                }
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy tổng quan tài chính',
                error: error.message
            })
        }
    }

    @Get('/get_financial_alerts')
    public async getFinancialAlerts({ response }: HttpContext) {
        try {
            const currentTime = Math.floor(Date.now() / 1000)
            const alerts = []

            // === CẢNH BÁO CÔNG NỢ QUÁ HẠN ===

            // 1. Monetization Debts Quá hạn  
            const overdueMonetizationQuery = Database.from('finance_monetization_debts')
                .select([
                    'network',
                    'network_receiver',
                    'network_code',
                    'amount',
                    'expected_date',
                    'month',
                    'year',
                    Database.raw(`FLOOR((${currentTime} - expected_date) / 86400) as overdueDays`)
                ])
                .where('amount_real', 0)
                .where('expected_date', '<', currentTime)
                .whereNotNull('expected_date')
                .orderBy('expected_date', 'asc')
                .limit(10)

            const overdueMonetization = await overdueMonetizationQuery

            for (const debt of overdueMonetization) {
                const networkName = debt.network_code || debt.network_receiver || debt.network
                const monthYear = `${debt.month}/${debt.year}`
                alerts.push({
                    id: `monetization_${networkName}_${debt.month}_${debt.year}`,
                    type: debt.overdueDays > 10 ? 'error' : 'warning',
                    title: `🔥 [MO] ${networkName} - Quá hạn thanh toán`,
                    description: `Công nợ Monetization từ ${networkName} tháng ${monthYear} đã quá hạn ${debt.overdueDays} ngày. Vui lòng kiểm tra và liên hệ.`,
                    amount: debt.amount,
                    currency: 'USD',
                    overdueDays: debt.overdueDays,
                    severity: debt.overdueDays > 15 ? 'critical' : debt.overdueDays > 7 ? 'high' : 'medium',
                    category: 'monetization_debt',
                    debtType: 'MONETIZATION',
                    actionRequired: true,
                    link: '/monetization_debts'
                })
            }

            // 2. UA Debts Quá hạn  
            const overdueUAQuery = Database.from('finance_ua_debts')
                .select([
                    'network',
                    'value',
                    'currency',
                    'due_date',
                    'month',
                    'year',
                    Database.raw(`FLOOR((${currentTime} - due_date) / 86400) as overdueDays`)
                ])
                .where('status', -1)
                .where('due_date', '<', currentTime)
                .whereNotNull('due_date')
                .orderBy('due_date', 'asc')
                .limit(10)

            const overdueUA = await overdueUAQuery

            for (const debt of overdueUA) {
                const monthYear = `${debt.month}/${debt.year}`
                // Convert to USD if needed - kiểm tra currency string thay vì currency_id  
                const amountUSD = debt.currency === 'USD' ? debt.value : debt.value / 24000

                alerts.push({
                    id: `ua_${debt.network}_${debt.month}_${debt.year}`,
                    type: debt.overdueDays > 10 ? 'error' : 'warning',
                    title: `🚨 [UA] ${debt.network} - Quá hạn thanh toán`,
                    description: `Công nợ User Acquisition (UA) từ ${debt.network} tháng ${monthYear} đã quá hạn ${debt.overdueDays} ngày. Số tiền gốc: ${this.formatCurrency(debt.value)} ${debt.currency}. Cần thanh toán gấp!`,
                    amount: amountUSD,
                    currency: 'USD',
                    originalAmount: debt.value,
                    originalCurrency: debt.currency,
                    overdueDays: debt.overdueDays,
                    severity: debt.overdueDays > 15 ? 'critical' : debt.overdueDays > 7 ? 'high' : 'medium',
                    category: 'ua_debt',
                    debtType: 'USER_ACQUISITION',
                    actionRequired: true,
                    link: '/ua_debts'
                })
            }

            // === CẢNH BÁO CÔNG NỢ CHƯA THANH TOÁN ===
            // Luôn hiển thị cảnh báo về công nợ chưa thanh toán nếu có

            // Nếu không có công nợ quá hạn, hiển thị công nợ chưa thanh toán
            if (alerts.filter(a => a.category === 'monetization_debt' || a.category === 'ua_debt').length === 0) {
                // Kiểm tra có công nợ Monetization chưa thanh toán không
                const pendingMonetizationQuery = Database.from('finance_monetization_debts')
                    .select([
                        'network',
                        'network_receiver',
                        'network_code',
                        'amount',
                        'expected_date',
                        'month',
                        'year'
                    ])
                    .where('amount_real', 0)
                    .orderBy('expected_date', 'asc')
                    .limit(5)

                const pendingMonetization = await pendingMonetizationQuery

                for (const debt of pendingMonetization) {
                    const networkName = debt.network_code || debt.network_receiver || debt.network
                    const monthYear = `${debt.month}/${debt.year}`
                    const daysUntilDue = debt.expected_date ? Math.floor((debt.expected_date - currentTime) / 86400) : null

                    alerts.push({
                        id: `pending_monetization_${networkName}_${debt.month}_${debt.year}`,
                        type: 'info',
                        title: `💰 [MO] ${networkName} - Chưa thanh toán`,
                        description: daysUntilDue && daysUntilDue > 0
                            ? `Công nợ Monetization từ ${networkName} tháng ${monthYear} sẽ đến hạn trong ${daysUntilDue} ngày. Vui lòng kiểm tra và liên hệ.`
                            : `Công nợ Monetization từ ${networkName} tháng ${monthYear} chưa được thanh toán. Vui lòng kiểm tra và liên hệ.`,
                        amount: debt.amount,
                        currency: 'USD',
                        severity: 'medium',
                        category: 'pending_debt',
                        debtType: 'MONETIZATION',
                        actionRequired: false,
                        link: '/monetization_debts'
                    })
                }

                // Kiểm tra có công nợ UA chưa thanh toán không
                const pendingUAQuery = Database.from('finance_ua_debts')
                    .select([
                        'network',
                        'value',
                        'currency',
                        'due_date',
                        'month',
                        'year'
                    ])
                    .where('status', -1)
                    .orderBy('due_date', 'asc')
                    .limit(5)

                const pendingUA = await pendingUAQuery

                for (const debt of pendingUA) {
                    const monthYear = `${debt.month}/${debt.year}`
                    const amountUSD = debt.currency === 'USD' ? debt.value : debt.value / 24000
                    const daysUntilDue = debt.due_date ? Math.floor((debt.due_date - currentTime) / 86400) : null

                    alerts.push({
                        id: `pending_ua_${debt.network}_${debt.month}_${debt.year}`,
                        type: 'info',
                        title: `📱 [UA] ${debt.network} - Chưa thanh toán`,
                        description: daysUntilDue && daysUntilDue > 0
                            ? `Công nợ User Acquisition (UA) từ ${debt.network} tháng ${monthYear} sẽ đến hạn trong ${daysUntilDue} ngày. Số tiền gốc: ${this.formatCurrency(debt.value)} ${debt.currency}. Cần chuẩn bị thanh toán!`
                            : `Công nợ User Acquisition (UA) từ ${debt.network} tháng ${monthYear} chưa được thanh toán. Số tiền gốc: ${this.formatCurrency(debt.value)} ${debt.currency}. Cần theo dõi!`,
                        amount: amountUSD,
                        currency: 'USD',
                        originalAmount: debt.value,
                        originalCurrency: debt.currency,
                        severity: 'medium',
                        category: 'pending_debt',
                        debtType: 'USER_ACQUISITION',
                        actionRequired: false,
                        link: '/ua_debts'
                    })
                }
            }

            // === THÔNG TIN TỔNG QUAN CÔNG NỢ ===
            // Luôn hiển thị thông tin tổng quan về công nợ hiện tại
            const totalMonetizationPending = await Database.from('finance_monetization_debts')
                .sum('amount as total')
                .where('amount_real', 0)
                .first()

            const totalUAPending = await Database.from('finance_ua_debts')
                .select(['value', 'currency', 'month', 'year'])
                .where('status', -1)

            const totalUAPendingUSD = await this.convertToUSD(totalUAPending)
            const totalMonetizationPendingAmount = totalMonetizationPending?.total || 0

            if (totalMonetizationPendingAmount > 0 || totalUAPendingUSD > 0) {
                const totalCombinedDebt = totalMonetizationPendingAmount + totalUAPendingUSD
                alerts.push({
                    id: 'debt_summary',
                    type: 'info',
                    title: '📊 TỔNG QUAN CÔNG NỢ HIỆN TẠI',
                    description: `💰 Monetization: ${this.formatCurrency(totalMonetizationPendingAmount)} | 📱 User Acquisition: ${this.formatCurrency(totalUAPendingUSD)} | 🔢 Tổng cộng: ${this.formatCurrency(totalCombinedDebt)}`,
                    amount: totalCombinedDebt,
                    monetizationAmount: totalMonetizationPendingAmount,
                    uaAmount: totalUAPendingUSD,
                    currency: 'USD',
                    severity: totalCombinedDebt > 100000 ? 'high' : 'medium',
                    category: 'debt_summary',
                    debtType: 'SUMMARY',
                    actionRequired: totalCombinedDebt > 50000,
                    link: null
                })
            }

            // === CẢNH BÁO SỐ DƯ THẤP ===

            // 3. Kiểm tra số dư tài khoản thấp
            const lowBalanceQuery = Database.from('finance_bank_accounts')
                .select(['account_name', 'current_balance', 'account_type', 'currency'])
                .where('current_balance', '<', 1000) // Ngưỡng cảnh báo: $100k
                .where('status', 1)

            const lowBalanceAccounts = await lowBalanceQuery

            for (const account of lowBalanceAccounts) {
                alerts.push({
                    id: `low_balance_${account.account_name}`,
                    type: account.balance < 50000 ? 'error' : 'warning',
                    title: `Số dư tài khoản ${account.account_name} thấp`,
                    description: `Tài khoản ${account.account_name} chỉ còn ${this.formatCurrency(account.balance)}. ${account.balance < 50000 ? 'Cần nạp tiền gấp.' : 'Vui lòng theo dõi và bổ sung.'}`,
                    amount: account.balance,
                    severity: account.balance < 25000 ? 'critical' : account.balance < 50000 ? 'high' : 'medium',
                    category: 'low_balance',
                    actionRequired: account.balance < 50000,
                    link: '/bank-accounts'
                })
            }

            // === CẢNH BÁO XU HƯỚNG ===

            // 4. Dự báo dòng tiền (tạm thời dùng logic đơn giản)
            const currentMonthStart = Math.floor(new Date(new Date().getFullYear(), new Date().getMonth(), 1).getTime() / 1000)
            const currentMonthEnd = Math.floor(new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getTime() / 1000)

            // Tổng chi phí UA tháng hiện tại
            const currentUACosts = await Database.from('finance_ua_debts')
                .sum('value as total')
                .where('currency', 'USD')
                .where('created', '>=', currentMonthStart)
                .where('created', '<=', currentMonthEnd)
                .first()

            const totalUACostsCurrent = currentUACosts?.total || 0

            // Tổng chi phí UA tháng trước
            const lastMonthStart = Math.floor(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1).getTime() / 1000)
            const lastMonthEnd = Math.floor(new Date(new Date().getFullYear(), new Date().getMonth(), 0).getTime() / 1000)

            const lastUACosts = await Database.from('finance_ua_debts')
                .sum('value as total')
                .where('currency', 'USD')
                .where('created', '>=', lastMonthStart)
                .where('created', '<=', lastMonthEnd)
                .first()

            const totalUACostsLast = lastUACosts?.total || 0

            if (totalUACostsLast > 0) {
                const changePercent = ((totalUACostsCurrent - totalUACostsLast) / totalUACostsLast * 100).toFixed(1)

                if (Math.abs(parseFloat(changePercent)) > 15) {
                    const isIncrease = parseFloat(changePercent) > 0
                    alerts.push({
                        id: 'ua_cost_trend',
                        type: 'info',
                        title: `Dự báo chi phí UA`,
                        description: `Chi phí UA tháng này ${isIncrease ? 'tăng' : 'giảm'} ${Math.abs(parseFloat(changePercent))}% so với tháng trước. ${isIncrease ? 'Cần theo dõi để tối ưu ROI.' : 'Xu hướng tích cực.'}`,
                        amount: totalUACostsCurrent,
                        changePercent: parseFloat(changePercent),
                        severity: Math.abs(parseFloat(changePercent)) > 25 ? 'high' : 'medium',
                        category: 'trend_forecast',
                        actionRequired: Math.abs(parseFloat(changePercent)) > 25,
                        link: '/cash-flow'
                    })
                }
            }

            // Sắp xếp alerts theo độ ưu tiên
            const priorityOrder: Record<string, number> = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 }
            alerts.sort((a: any, b: any) => priorityOrder[b.severity] - priorityOrder[a.severity])

            // Lấy alerts để hiển thị (tối đa 15)
            const displayedAlerts = alerts.slice(0, 15)

            return response.json({
                success: true,
                data: {
                    alerts: displayedAlerts,
                    summary: {
                        total: alerts.length, // Tổng số alerts thực tế
                        displayed: displayedAlerts.length, // Số alerts hiển thị
                        critical: displayedAlerts.filter(a => a.severity === 'critical').length,
                        high: displayedAlerts.filter(a => a.severity === 'high').length,
                        medium: displayedAlerts.filter(a => a.severity === 'medium').length,
                        actionRequired: displayedAlerts.filter(a => a.actionRequired).length,
                        byDebtType: {
                            monetization: displayedAlerts.filter(a => a.debtType === 'MONETIZATION').length,
                            userAcquisition: displayedAlerts.filter(a => a.debtType === 'USER_ACQUISITION').length,
                            summary: displayedAlerts.filter(a => a.debtType === 'SUMMARY').length,
                            others: displayedAlerts.filter(a => !a.debtType || (a.debtType !== 'MONETIZATION' && a.debtType !== 'USER_ACQUISITION' && a.debtType !== 'SUMMARY')).length
                        },
                        byCategory: {
                            monetizationDebt: displayedAlerts.filter(a => a.category === 'monetization_debt').length,
                            uaDebt: displayedAlerts.filter(a => a.category === 'ua_debt').length,
                            pendingDebt: displayedAlerts.filter(a => a.category === 'pending_debt').length,
                            debtSummary: displayedAlerts.filter(a => a.category === 'debt_summary').length,
                            lowBalance: displayedAlerts.filter(a => a.category === 'low_balance').length,
                            trendForecast: displayedAlerts.filter(a => a.category === 'trend_forecast').length
                        }
                    },
                    lastUpdated: new Date().toISOString()
                }
            })
        } catch (error) {
            console.log(error)
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy cảnh báo tài chính',
                error: error.message
            })
        }
    }

    @Get('/get_expense_details')
    public async getExpenseDetails({ request, response }: HttpContext) {
        try {
            const {
                timeFrame = 'month',
                period = 'current',
                selectedMonth,
                selectedYear,
                selectedQuarter
            } = request.qs()

            // Tính toán khoảng thời gian
            const { currentPeriod } = this.calculatePeriods(
                timeFrame,
                period,
                selectedMonth,
                selectedYear,
                selectedQuarter
            )

            const query = Database.from('finance_cost_details')
                .select(['name', 'value', 'date'])

            // Thêm filter theo thời gian sử dụng cột date
            if (timeFrame === 'month') {
                const month = currentPeriod.month || 1
                const year = currentPeriod.year || new Date().getFullYear()
                const startDate = `${year}-${month.toString().padStart(2, '0')}-01`
                const endDate = `${year}-${month.toString().padStart(2, '0')}-31`
                query.whereBetween('date', [startDate, endDate])
            } else if (timeFrame === 'quarter') {
                const quarter = currentPeriod.quarter || 1
                const year = currentPeriod.year || new Date().getFullYear()
                const startMonth = (quarter - 1) * 3 + 1
                const endMonth = quarter * 3
                const startDate = `${year}-${startMonth.toString().padStart(2, '0')}-01`
                const endDate = `${year}-${endMonth.toString().padStart(2, '0')}-31`
                query.whereBetween('date', [startDate, endDate])
            } else {
                const year = currentPeriod.year || new Date().getFullYear()
                query.whereRaw('YEAR(date) = ?', [year])
            }

            const rawData = await query

            // Group by name và tính tổng
            const expenseGroups: Record<string, number> = {}
            rawData.forEach(item => {
                const expenseName = item.name || 'Khác'
                if (!expenseGroups[expenseName]) {
                    expenseGroups[expenseName] = 0
                }
                expenseGroups[expenseName] += parseFloat(item.value) || 0
            })

            // Tính tổng để tính phần trăm
            const totalExpense = Object.values(expenseGroups).reduce((sum, value) => sum + value, 0)

            // Tạo dữ liệu kết quả
            const expenseData = []
            for (const [expenseName, totalValue] of Object.entries(expenseGroups)) {
                expenseData.push({
                    name: expenseName,
                    value: Math.round(totalValue * 100) / 100,
                    percentage: totalExpense > 0 ? Math.round((totalValue / totalExpense * 100) * 100) / 100 : 0
                })
            }

            // Sort by value desc
            expenseData.sort((a, b) => b.value - a.value)

            return response.json({
                success: true,
                data: expenseData
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy chi tiết chi phí',
                error: error.message
            })
        }
    }

    // Sample data insertion (temporary - chỉ để test)
    @Get('/insert_sample_expense_data')
    public async insertSampleExpenseData({ response }: HttpContext) {
        try {
            // Xóa dữ liệu cũ
            await Database.from('finance_cost_details').delete()

            // Thêm sample data cho 2024-2025
            const sampleData = [
                // July 2024
                { name: 'Facebook Ads', value: 150000000, date: '2024-07-15', expense_type_id: 2, squad_id: 4, department_id: 3 },
                { name: 'Google Ads', value: 120000000, date: '2024-07-15', expense_type_id: 2, squad_id: 4, department_id: 3 },
                { name: 'TikTok Ads', value: 80000000, date: '2024-07-15', expense_type_id: 2, squad_id: 3, department_id: 3 },
                { name: 'Unity Ads', value: 60000000, date: '2024-07-15', expense_type_id: 2, squad_id: 5, department_id: 3 },
                { name: 'Apple Search Ads', value: 45000000, date: '2024-07-15', expense_type_id: 2, squad_id: 5, department_id: 3 },

                // November 2024 (tháng trước hiện tại)
                { name: 'Facebook Ads', value: 180000000, date: '2024-11-15', expense_type_id: 2, squad_id: 4, department_id: 3 },
                { name: 'Google Ads', value: 140000000, date: '2024-11-15', expense_type_id: 2, squad_id: 4, department_id: 3 },
                { name: 'TikTok Ads', value: 95000000, date: '2024-11-15', expense_type_id: 2, squad_id: 3, department_id: 3 },
                { name: 'Unity Ads', value: 70000000, date: '2024-11-15', expense_type_id: 2, squad_id: 5, department_id: 3 },
                { name: 'Apple Search Ads', value: 55000000, date: '2024-11-15', expense_type_id: 2, squad_id: 5, department_id: 3 },
                { name: 'Snapchat Ads', value: 30000000, date: '2024-11-15', expense_type_id: 2, squad_id: 2, department_id: 3 },

                // December 2024 (tháng hiện tại)
                { name: 'Facebook Ads', value: 200000000, date: '2024-12-15', expense_type_id: 2, squad_id: 4, department_id: 3 },
                { name: 'Google Ads', value: 160000000, date: '2024-12-15', expense_type_id: 2, squad_id: 4, department_id: 3 },
                { name: 'TikTok Ads', value: 110000000, date: '2024-12-15', expense_type_id: 2, squad_id: 3, department_id: 3 },
                { name: 'Unity Ads', value: 85000000, date: '2024-12-15', expense_type_id: 2, squad_id: 5, department_id: 3 },
                { name: 'Apple Search Ads', value: 65000000, date: '2024-12-15', expense_type_id: 2, squad_id: 5, department_id: 3 },
                { name: 'Snapchat Ads', value: 40000000, date: '2024-12-15', expense_type_id: 2, squad_id: 2, department_id: 3 },
                { name: 'Pinterest Ads', value: 25000000, date: '2024-12-15', expense_type_id: 2, squad_id: 2, department_id: 3 },

                // January 2025
                { name: 'Facebook Ads', value: 220000000, date: '2025-01-15', expense_type_id: 2, squad_id: 4, department_id: 3 },
                { name: 'Google Ads', value: 180000000, date: '2025-01-15', expense_type_id: 2, squad_id: 4, department_id: 3 },
                { name: 'TikTok Ads', value: 125000000, date: '2025-01-15', expense_type_id: 2, squad_id: 3, department_id: 3 },
                { name: 'Unity Ads', value: 95000000, date: '2025-01-15', expense_type_id: 2, squad_id: 5, department_id: 3 },
                { name: 'Apple Search Ads', value: 75000000, date: '2025-01-15', expense_type_id: 2, squad_id: 5, department_id: 3 },
                { name: 'Snapchat Ads', value: 50000000, date: '2025-01-15', expense_type_id: 2, squad_id: 2, department_id: 3 },
                { name: 'Pinterest Ads', value: 35000000, date: '2025-01-15', expense_type_id: 2, squad_id: 2, department_id: 3 },
                { name: 'LinkedIn Ads', value: 20000000, date: '2025-01-15', expense_type_id: 2, squad_id: 2, department_id: 3 }
            ]

            await Database.table('finance_cost_details').insert(sampleData)

            return response.json({
                success: true,
                message: 'Sample data inserted successfully',
                count: sampleData.length
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi chèn sample data',
                error: error.message
            })
        }
    }

    // Helper methods
    private calculatePeriods(timeFrame: string, period: string, selectedMonth?: string, selectedYear?: string, selectedQuarter?: string) {
        const currentDate = new Date()
        const currentYear = parseInt(selectedYear || currentDate.getFullYear().toString())
        const currentMonth = parseInt(selectedMonth || (currentDate.getMonth() + 1).toString())
        const currentQuarter = parseInt(selectedQuarter || Math.ceil(currentDate.getMonth() / 3).toString())

        let currentPeriod

        if (timeFrame === 'month') {
            if (period === 'current') {
                currentPeriod = { month: currentMonth, year: currentYear }
            } else {
                // Previous period
                currentPeriod = currentMonth > 1
                    ? { month: currentMonth - 1, year: currentYear }
                    : { month: 12, year: currentYear - 1 }
            }
        } else if (timeFrame === 'quarter') {
            if (period === 'current') {
                currentPeriod = { quarter: currentQuarter, year: currentYear }
            } else {
                // Previous period
                currentPeriod = currentQuarter > 1
                    ? { quarter: currentQuarter - 1, year: currentYear }
                    : { quarter: 4, year: currentYear - 1 }
            }
        } else {
            if (period === 'current') {
                currentPeriod = { year: currentYear }
            } else {
                currentPeriod = { year: currentYear - 1 }
            }
        }

        return { currentPeriod }
    }

    private calculatePeriodsForFinancialOverview(timeFrame: string, period: string, selectedMonth?: string, selectedYear?: string, selectedQuarter?: string) {
        const currentDate = new Date()
        const currentYear = parseInt(selectedYear || currentDate.getFullYear().toString())
        const currentMonth = parseInt(selectedMonth || (currentDate.getMonth() + 1).toString())
        const currentQuarter = parseInt(selectedQuarter || Math.ceil(currentDate.getMonth() / 3).toString())

        let currentPeriod
        let previousPeriod

        if (timeFrame === 'month') {
            if (period === 'current') {
                currentPeriod = { month: currentMonth, year: currentYear }
                previousPeriod = { month: currentMonth - 1, year: currentYear }
            } else {
                // Previous period
                currentPeriod = currentMonth > 1
                    ? { month: currentMonth - 1, year: currentYear }
                    : { month: 12, year: currentYear - 1 }
                previousPeriod = currentPeriod
            }
        } else if (timeFrame === 'quarter') {
            if (period === 'current') {
                currentPeriod = { quarter: currentQuarter, year: currentYear }
                previousPeriod = { quarter: currentQuarter - 1, year: currentYear }
            } else {
                // Previous period
                currentPeriod = currentQuarter > 1
                    ? { quarter: currentQuarter - 1, year: currentYear }
                    : { quarter: 4, year: currentYear - 1 }
                previousPeriod = currentPeriod
            }
        } else {
            if (period === 'current') {
                currentPeriod = { year: currentYear }
                previousPeriod = { year: currentYear - 1 }
            } else {
                currentPeriod = { year: currentYear - 1 }
                previousPeriod = currentPeriod
            }
        }

        return { currentPeriod, previousPeriod }
    }

    private addPeriodFilter(query: any, period: any, timeFrame: string) {
        if (timeFrame === 'month') {
            query.where('month', period.month).where('year', period.year)
        } else if (timeFrame === 'quarter') {
            query.where('quarter', period.quarter).where('year', period.year)
        } else {
            query.where('year', period.year)
        }
    }

    /**
     * Chuyển đổi danh sách amounts về USD dựa trên tỷ giá
     */
    private async convertToUSD(debts: any[]): Promise<number> {
        let totalUSD = 0

        for (const debt of debts) {
            if (debt.currency === 'USD') {
                totalUSD += parseFloat(debt.value) || 0
            } else {
                let rate = null

                // Chỉ tìm tỷ giá nếu có month và year
                if (debt.month && debt.year) {
                    // Tìm tỷ giá cho currency và tháng cụ thể
                    const monthKey = `${debt.year}-${debt.month.toString().padStart(2, '0')}`
                    rate = await FinanceCurrenciesMonth.query()
                        .where('name', debt.currency)
                        .where('month', monthKey)
                        .first()
                }

                if (rate && rate.rate > 0) {
                    totalUSD += (parseFloat(debt.value) || 0) / rate.rate
                } else {
                    // Fallback rate nếu không tìm thấy
                    const fallbackRates: Record<string, number> = {
                        'VND': 24000,
                        'COP': 4000,
                        'EUR': 0.85,
                        'GBP': 0.75
                    }
                    const fallbackRate = fallbackRates[debt.currency] || 1
                    totalUSD += (parseFloat(debt.value) || 0) / fallbackRate
                }
            }
        }

        return Math.round(totalUSD * 100) / 100
    }

    // Helper method để format currency
    private formatCurrency(amount: number): string {
        return `$${amount.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        })}`
    }
}