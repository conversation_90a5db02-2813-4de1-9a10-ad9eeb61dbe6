import * as Sentry from '@sentry/node'
import { Get, Post } from '#libraries/vcms/route_decorators'
import { HttpContext } from '@adonisjs/core/http'

export default class SentryExampleController {
    /**
     * V<PERSON> dụ bắt và ghi nhận lỗi
     */
    @Get('/example/error')
    public async exampleError({ response }: HttpContext) {
        try {
            // Mô phỏng lỗi
            throw new Error('Đây là một lỗi được mô phỏng')
        } catch (error) {
            // Ghi nhận lỗi với Sentry
            Sentry.captureException(error)

            return response.status(500).json({
                status: 'error',
                message: 'Có lỗi xảy ra',
                error: error.message
            })
        }
    }

    /**
     * Ví dụ ghi nhận message
     */
    @Get('/example/message')
    public async exampleMessage({ response }: HttpContext) {
        // Ghi nhận một thông báo với Sentry
        Sentry.captureMessage('Đ<PERSON><PERSON> là một thông báo từ controller', 'info')

        return response.status(200).json({
            status: 'success',
            message: 'Đ<PERSON> ghi nhận thông báo'
        })
    }

    /**
     * <PERSON><PERSON> dụ thêm thông tin bổ sung vào event
     */
    @Post('/example/context')
    public async exampleContext({ request, response }: HttpContext) {
        const data = request.body()

        try {
            // Thêm thông tin context
            Sentry.setContext('user_input', data)

            // Thêm tag để lọc theo tag này trong Sentry dashboard
            Sentry.setTag('feature', 'payment')

            // Thêm breadcrumb để theo dõi luồng xử lý
            Sentry.addBreadcrumb({
                category: 'payment',
                message: 'Bắt đầu xử lý thanh toán',
                level: 'info'
            })

            // Ghi nhận hành động của người dùng
            if (data.amount <= 0) {
                Sentry.addBreadcrumb({
                    category: 'payment',
                    message: 'Số tiền không hợp lệ',
                    level: 'warning'
                })

                throw new Error('Số tiền thanh toán không hợp lệ')
            }

            // Ghi nhận thành công
            Sentry.addBreadcrumb({
                category: 'payment',
                message: 'Xử lý thanh toán thành công',
                level: 'info'
            })

            return response.status(200).json({
                status: 'success',
                message: 'Xử lý thanh toán thành công'
            })
        } catch (error) {
            Sentry.captureException(error)

            return response.status(400).json({
                status: 'error',
                message: 'Xử lý thanh toán thất bại',
                error: error.message
            })
        }
    }
} 