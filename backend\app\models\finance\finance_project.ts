import { BaseModel, column, beforeSave } from '@adonisjs/lucid/orm'
import Helper from '#libraries/vcms/helper'

export default class FinanceProject extends BaseModel {
    public static table = 'finance_project'

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare squad_id: number | null

    @column()
    declare name: number

    @column()
    declare app_id: number

    @column()
    declare type: number

    @column()
    declare platform: number

    @column()
    declare updated: number

    @column()
    declare created: number

    @beforeSave()
    public static async autoTimestamp(data: FinanceProject) {
        if (!data.created) {
            data.created = Helper.now_timestamp()
        }
        data.updated = Helper.now_timestamp()
    }
} 