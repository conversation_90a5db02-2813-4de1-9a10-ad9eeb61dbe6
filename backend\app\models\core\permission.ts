import { column, BaseModel } from '@adonisjs/lucid/orm'
import ColumnData from '#models/core/types/column_data'
import { DateTime } from 'luxon'
export default class Permission extends BaseModel {
    public static table = 'permissions'
    public static columns: Record<string, ColumnData> = {
        id: {
            type: 'bigint',
            length: 20,
            required: true,
            column: 12,
            listing: true,
        },
        slug: {
            type: 'varchar',
            length: 255,
            column: 12,
            listing: true,
        },
        title: {
            type: 'varchar',
            length: 255,
            column: 12,
            listing: true,
        },
        entityType: {
            type: 'varchar',
            length: 255,
            column: 12,
            listing: true,
        },
        entityId: {
            type: 'bigint',
            length: 20,
            column: 12,
            listing: true,
        },
        scope: {
            type: 'varchar',
            length: 255,
            column: 12,
            listing: true,
        },
        allowed: {
            type: 'tinyint',
            length: 1,
            column: 12,
            listing: true,
        },
        in_group: {
            type: 'int',
            column: 12,
            listing: true,
        },
        permission_order: {
            type: 'int',
            column: 12,
            listing: true,
        },
        createdAt: {
            type: 'timestamp',
            invisible: true,
        },
        updatedAt: {
            type: 'timestamp',
            invisible: true,
        },
    }

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare slug: string

    @column()
    declare title: string

    @column()
    declare entity_type: string

    @column()
    declare entity_id: number

    @column()
    declare scope: string

    @column()
    declare allowed: number

    @column()
    declare in_group: number

    @column()
    declare permission_order: number

    @column.dateTime({ autoCreate: true })
    declare createdAt: DateTime

    @column.dateTime({ autoCreate: true, autoUpdate: true })
    declare updatedAt: DateTime
}
