import { column, BaseModel } from '@adonisjs/lucid/orm'
import ColumnData from '#models/core/types/column_data'
import { DateTime } from 'luxon'
export default class Role extends BaseModel {
    public static table = 'roles'
    public static columns: Record<string, ColumnData> = {
        id: {
            type: 'bigint',
            length: 20,
            required: true,
            column: 12,
            listing: false,
        },
        title: {
            type: 'varchar',
            length: 255,
            required: true,
            column: 6,
            listing: {
                template: '<span style="color: {color}; font-weight: bold">{title}</span>',
                sortable: true,
            },
        },
        slug: {
            type: 'varchar',
            required: true,
            length: 255,
            column: 6,
            listing: true,
        },
        color: {
            type: 'varchar',
            edit_type: 'color',
            length: 20,
            required: true,
            column: 4,
        },
        entityType: {
            type: 'varchar',
            length: 255,
            column: 4,
        },
        entityId: {
            type: 'bigint',
            length: 20,
            column: 4,
        },
        scope: {
            type: 'varchar',
            length: 255,
            column: 6,
            listing: true,
        },
        allowed: {
            type: 'tinyint',
            length: 1,
            column: 6,
            listing: {
                type: 'status',
                enum: {
                    0: 'No',
                    1: 'Yes',
                },
            },
        },
        createdAt: {
            type: 'timestamp',
            invisible: true,
        },
        updatedAt: {
            type: 'timestamp',
            invisible: true,
        },
    }

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare slug: string

    @column()
    declare title: string

    @column()
    declare color: string

    @column()
    declare entityType: string

    @column()
    declare entityId: number

    @column()
    declare scope: string

    @column()
    declare allowed: number

    @column.dateTime({ autoCreate: true })
    declare createdAt: DateTime

    @column.dateTime({ autoCreate: true, autoUpdate: true })
    declare updatedAt: DateTime
}
