import { BaseModel, column } from '@adonisjs/lucid/orm'
import ColumnData from '#models/core/types/column_data'

export default class MenuItem extends BaseModel {
    public static table = 'menu_items'
    public static columns: Record<string, ColumnData> = {
        id: {
            type: 'int',
            length: 11,
            required: true,
        },
        menu_id: {
            type: 'int',
            length: 11,
            required: true,
        },
        parrent_id: {
            type: 'int',
            length: 11,
            required: true,
        },
        code: {
            type: 'varchar',
            length: 255,
            required: false,
        },
        title: {
            type: 'varchar',
            length: 255,
            required: true,
        },
        icon: {
            type: 'varchar',
            length: 255,
            required: false,
        },
        order: {
            type: 'int',
            length: 11,
            required: false,
        },
    }

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare menu_id: number

    @column()
    declare parrent_id: number

    @column()
    declare code: string

    @column()
    declare title: string

    @column()
    declare icon: string

    @column()
    declare order: number
}
