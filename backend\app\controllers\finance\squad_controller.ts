// import type { HttpContext } from '@adonisjs/core/http'

import { Get, ApiOnly, Prefix } from '#libraries/vcms/route_decorators'
import { HttpContext } from '@adonisjs/core/http'
import Database from '@adonisjs/lucid/services/db'

@ApiOnly()
@Prefix('/api/squad')
export default class SquadController {

    /**
     * Lấy tổng quan tài chính các Squad từ finance_revenue và finance_cost_details
     */
    @Get('/overview')
    public async getSquadOverview({ request, response }: HttpContext) {
        try {
            const { timeFrame = 'month', period = 'current', month = 6, quarter = 2, year = 2023 } = request.qs()

            // Tính toán khoảng thời gian
            const { periodValue } = this.calculatePeriod(timeFrame, period, month, quarter, year)

            // Lấy danh sách squad active
            const squads = await Database
                .from('finance_squads')
                .where('status', 'active')
                .select(['id', 'name'])
                .orderBy('name', 'asc')

            const squadResults = []

            for (const squad of squads) {
                // Lấy tổng doanh thu từ finance_revenue
                let revenueQuery = Database
                    .from('finance_revenue')
                    .where('squad_id', squad.id)
                    .sum('value as total_revenue')

                // Thêm filter theo timeFrame
                if (timeFrame === 'month') {
                    const [year, month] = periodValue.split('-')
                    revenueQuery = revenueQuery.where('month', parseInt(month)).where('year', parseInt(year))
                } else if (timeFrame === 'quarter') {
                    const [year, quarter] = periodValue.split('-Q')
                    revenueQuery = revenueQuery.where('quarter', parseInt(quarter)).where('year', parseInt(year))
                } else {
                    revenueQuery = revenueQuery.where('year', parseInt(periodValue))
                }

                const revenueResult = await revenueQuery.first()
                const totalRevenue = Number(revenueResult?.total_revenue || 0)

                // Lấy tổng chi phí từ finance_cost_details
                let costQuery = Database
                    .from('finance_cost_details')
                    .where('squad_id', squad.id)
                    .sum('value as total_cost')

                // Thêm filter theo timeFrame
                if (timeFrame === 'month') {
                    const [year, month] = periodValue.split('-')
                    costQuery = costQuery.where('month', parseInt(month)).where('year', parseInt(year))
                } else if (timeFrame === 'quarter') {
                    // Tính các tháng trong quý
                    const [year, quarter] = periodValue.split('-Q')
                    const quarterNum = parseInt(quarter)
                    const monthsInQuarter = [(quarterNum - 1) * 3 + 1, (quarterNum - 1) * 3 + 2, (quarterNum - 1) * 3 + 3]
                    costQuery = costQuery.whereIn('month', monthsInQuarter).where('year', parseInt(year))
                } else {
                    costQuery = costQuery.where('year', parseInt(periodValue))
                }

                const costResult = await costQuery.first()
                const totalCost = Number(costResult?.total_cost || 0)

                // Lấy chi phí trực tiếp (type = 1) và marketing (type = 2)
                let directCostQuery = Database
                    .from('finance_cost_details')
                    .where('squad_id', squad.id)
                    .where('type', 1)
                    .sum('value as direct_cost')

                let marketingCostQuery = Database
                    .from('finance_cost_details')
                    .where('squad_id', squad.id)
                    .where('type', 2)
                    .sum('value as marketing_cost')

                // Thêm filter theo timeFrame cho chi phí chi tiết
                if (timeFrame === 'month') {
                    const [year, month] = periodValue.split('-')
                    directCostQuery = directCostQuery.where('month', parseInt(month)).where('year', parseInt(year))
                    marketingCostQuery = marketingCostQuery.where('month', parseInt(month)).where('year', parseInt(year))
                } else if (timeFrame === 'quarter') {
                    const [year, quarter] = periodValue.split('-Q')
                    const quarterNum = parseInt(quarter)
                    const monthsInQuarter = [(quarterNum - 1) * 3 + 1, (quarterNum - 1) * 3 + 2, (quarterNum - 1) * 3 + 3]
                    directCostQuery = directCostQuery.whereIn('month', monthsInQuarter).where('year', parseInt(year))
                    marketingCostQuery = marketingCostQuery.whereIn('month', monthsInQuarter).where('year', parseInt(year))
                } else {
                    directCostQuery = directCostQuery.where('year', parseInt(periodValue))
                    marketingCostQuery = marketingCostQuery.where('year', parseInt(periodValue))
                }

                const directCostResult = await directCostQuery.first()
                const marketingCostResult = await marketingCostQuery.first()

                const directCost = Number(directCostResult?.direct_cost || 0)
                const marketingCost = Number(marketingCostResult?.marketing_cost || 0)

                // Tính toán lợi nhuận và ROI
                const profit = totalRevenue - totalCost
                const roi = totalCost > 0 ? (profit / totalCost * 100) : 0

                // Đếm số dự án (giả sử có bảng finance_squad_projects)
                let projectCountQuery = Database
                    .from('finance_revenue')
                    .where('squad_id', squad.id)
                    .countDistinct('project_id as project_count')

                if (timeFrame === 'month') {
                    const [year, month] = periodValue.split('-')
                    projectCountQuery = projectCountQuery.where('month', parseInt(month)).where('year', parseInt(year))
                } else if (timeFrame === 'quarter') {
                    const [year, quarter] = periodValue.split('-Q')
                    const quarterNum = parseInt(quarter)
                    const monthsInQuarter = [(quarterNum - 1) * 3 + 1, (quarterNum - 1) * 3 + 2, (quarterNum - 1) * 3 + 3]
                    projectCountQuery = projectCountQuery.whereIn('month', monthsInQuarter).where('year', parseInt(year))
                } else {
                    projectCountQuery = projectCountQuery.where('year', parseInt(periodValue))
                }

                const projectCountResult = await projectCountQuery.first()
                const projectCount = Number(projectCountResult?.project_count || 0)

                squadResults.push({
                    id: squad.id,
                    name: squad.name,
                    revenue: totalRevenue,
                    directCost: directCost,
                    marketingCost: marketingCost,
                    totalCost: totalCost,
                    profit: profit,
                    roi: Math.round(roi * 100) / 100,
                    projects: projectCount
                })
            }

            // Sắp xếp theo doanh thu giảm dần
            squadResults.sort((a, b) => b.revenue - a.revenue)

            // Tính tổng
            const totals = squadResults.reduce((acc, squad) => ({
                totalRevenue: acc.totalRevenue + squad.revenue,
                totalDirectCost: acc.totalDirectCost + squad.directCost,
                totalMarketingCost: acc.totalMarketingCost + squad.marketingCost,
                totalCost: acc.totalCost + squad.totalCost,
                totalProfit: acc.totalProfit + squad.profit
            }), {
                totalRevenue: 0,
                totalDirectCost: 0,
                totalMarketingCost: 0,
                totalCost: 0,
                totalProfit: 0
            })

            return response.json({
                success: true,
                data: {
                    totals,
                    squadsData: squadResults,
                    period: {
                        timeFrame,
                        periodValue,
                        period
                    }
                }
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy tổng quan Squad',
                error: error.message
            })
        }
    }

    /**
     * Lấy danh sách Squad
     */
    @Get('/list')
    public async getSquadList({ response }: HttpContext) {
        try {
            const squads = await Database
                .from('finance_squads')
                .where('status', 'active')
                .select(['id', 'name', 'slug', 'description'])
                .orderBy('name', 'asc')

            return response.json({
                success: true,
                data: squads
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy danh sách Squad',
                error: error.message
            })
        }
    }

    /**
     * Lấy chi tiết dự án của một Squad
     */
    @Get('/projects/:squadId')
    public async getSquadProjects({ params, request, response }: HttpContext) {
        try {
            const { squadId } = params
            const { timeFrame = 'month', month = 6, quarter = 2, year = 2023 } = request.qs()

            // Lấy thông tin squad
            const squad = await Database
                .from('finance_squads')
                .where('id', squadId)
                .where('status', 'active')
                .first()

            if (!squad) {
                return response.status(404).json({
                    success: false,
                    message: 'Không tìm thấy Squad'
                })
            }

            // Lấy danh sách dự án duy nhất từ finance_revenue
            let projectQuery = Database
                .from('finance_revenue as fr')
                .where('fr.squad_id', squadId)
                .whereNotNull('fr.project_id')
                .select(['fr.project_id', 'fr.name as project_name'])
                .groupBy(['fr.project_id', 'fr.name'])

            // Thêm filter theo timeFrame
            if (timeFrame === 'month') {
                projectQuery = projectQuery.where('fr.month', month).where('fr.year', year)
            } else if (timeFrame === 'quarter') {
                const monthsInQuarter = [(quarter - 1) * 3 + 1, (quarter - 1) * 3 + 2, (quarter - 1) * 3 + 3]
                projectQuery = projectQuery.whereIn('fr.month', monthsInQuarter).where('fr.year', year)
            } else {
                projectQuery = projectQuery.where('fr.year', year)
            }

            const projects = await projectQuery

            const projectsData = []

            for (const project of projects) {
                // Lấy tổng doanh thu của dự án
                let revenueQuery = Database
                    .from('finance_revenue')
                    .where('squad_id', squadId)
                    .where('project_id', project.project_id)
                    .sum('value as total_revenue')

                // Lấy tổng chi phí của dự án
                let costQuery = Database
                    .from('finance_cost_details')
                    .where('squad_id', squadId)
                    .where('project_id', project.project_id)
                    .sum('value as total_cost')

                // Thêm filter theo timeFrame
                if (timeFrame === 'month') {
                    revenueQuery = revenueQuery.where('month', month).where('year', year)
                    costQuery = costQuery.where('month', month).where('year', year)
                } else if (timeFrame === 'quarter') {
                    const monthsInQuarter = [(quarter - 1) * 3 + 1, (quarter - 1) * 3 + 2, (quarter - 1) * 3 + 3]
                    revenueQuery = revenueQuery.whereIn('month', monthsInQuarter).where('year', year)
                    costQuery = costQuery.whereIn('month', monthsInQuarter).where('year', year)
                } else {
                    revenueQuery = revenueQuery.where('year', year)
                    costQuery = costQuery.where('year', year)
                }

                const revenueResult = await revenueQuery.first()
                const costResult = await costQuery.first()

                const revenue = Number(revenueResult?.total_revenue || 0)
                const totalCost = Number(costResult?.total_cost || 0)
                const profit = revenue - totalCost
                const roi = totalCost > 0 ? (profit / totalCost * 100) : 0

                projectsData.push({
                    id: project.project_id,
                    name: project.project_name || `Dự án ${project.project_id}`,
                    revenue: revenue,
                    directCost: totalCost, // Tạm thời coi tất cả là direct cost
                    marketingCost: 0, // Có thể tính riêng nếu cần
                    profit: profit,
                    roi: Math.round(roi * 100) / 100,
                    status: this.getStatusLabel('active') // Mặc định là active
                })
            }

            // Sắp xếp theo doanh thu giảm dần
            projectsData.sort((a, b) => b.revenue - a.revenue)

            return response.json({
                success: true,
                data: {
                    squad: {
                        id: squad.id,
                        name: squad.name
                    },
                    projects: projectsData
                }
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy dự án Squad',
                error: error.message
            })
        }
    }

    /**
     * Lấy xu hướng doanh thu theo Squad
     */
    @Get('/trend')
    public async getSquadTrend({ request, response }: HttpContext) {
        try {
            const {
                timeFrame = 'month',
                fromMonth = 1,
                fromYear = 2023,
                toMonth = 6,
                toYear = 2023
            } = request.qs()

            // Lấy danh sách squad active
            const squads = await Database
                .from('finance_squads')
                .where('status', 'active')
                .select(['id', 'name'])
                .orderBy('name', 'asc')

            let trendData = []

            if (timeFrame === 'month') {
                // Tạo danh sách các tháng trong khoảng thời gian
                const months = []
                let currentYear = parseInt(fromYear)
                let currentMonth = parseInt(fromMonth)
                const endYear = parseInt(toYear)
                const endMonth = parseInt(toMonth)

                while (currentYear < endYear || (currentYear === endYear && currentMonth <= endMonth)) {
                    months.push({ month: currentMonth, year: currentYear })
                    currentMonth++
                    if (currentMonth > 12) {
                        currentMonth = 1
                        currentYear++
                    }
                }

                // Lấy dữ liệu cho từng tháng
                for (const monthData of months) {
                    const periodValue = `${monthData.year}-${String(monthData.month).padStart(2, '0')}`
                    const periodObj: any = { period: periodValue }

                    for (const squad of squads) {
                        const revenueResult = await Database
                            .from('finance_revenue')
                            .where('squad_id', squad.id)
                            .where('month', monthData.month)
                            .where('year', monthData.year)
                            .sum('value as total_revenue')
                            .first()

                        periodObj[squad.name] = Number(revenueResult?.total_revenue || 0)
                    }

                    trendData.push(periodObj)
                }
            }

            return response.json({
                success: true,
                data: trendData
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy xu hướng Squad',
                error: error.message
            })
        }
    }

    /**
     * Lấy dữ liệu so sánh Squad
     */
    @Get('/comparison')
    public async getSquadComparison({ request, response }: HttpContext) {
        try {
            const { timeFrame = 'month', month = 6, quarter = 2, year = 2023 } = request.qs()

            const { periodValue: currentPeriod } = this.calculatePeriod(timeFrame, 'current', month, quarter, year)
            const { periodValue: previousPeriod } = this.calculatePeriod(timeFrame, 'previous', month, quarter, year)

            // Lấy danh sách squad active
            const squads = await Database
                .from('finance_squads')
                .where('status', 'active')
                .select(['id', 'name'])
                .orderBy('name', 'asc')

            const comparisonData = []

            for (const squad of squads) {
                // Lấy dữ liệu hiện tại
                let currentRevenueQuery = Database
                    .from('finance_revenue')
                    .where('squad_id', squad.id)
                    .sum('value as total_revenue')

                let currentCostQuery = Database
                    .from('finance_cost_details')
                    .where('squad_id', squad.id)
                    .sum('value as total_cost')

                // Lấy dữ liệu kỳ trước
                let previousRevenueQuery = Database
                    .from('finance_revenue')
                    .where('squad_id', squad.id)
                    .sum('value as total_revenue')

                let previousCostQuery = Database
                    .from('finance_cost_details')
                    .where('squad_id', squad.id)
                    .sum('value as total_cost')

                // Thêm filter theo timeFrame
                if (timeFrame === 'month') {
                    const [currentYear, currentMonth] = currentPeriod.split('-')
                    const [previousYear, previousMonth] = previousPeriod.split('-')

                    currentRevenueQuery = currentRevenueQuery.where('month', parseInt(currentMonth)).where('year', parseInt(currentYear))
                    currentCostQuery = currentCostQuery.where('month', parseInt(currentMonth)).where('year', parseInt(currentYear))
                    previousRevenueQuery = previousRevenueQuery.where('month', parseInt(previousMonth)).where('year', parseInt(previousYear))
                    previousCostQuery = previousCostQuery.where('month', parseInt(previousMonth)).where('year', parseInt(previousYear))
                } else if (timeFrame === 'quarter') {
                    const [currentYear, currentQuarter] = currentPeriod.split('-Q')
                    const [previousYear, previousQuarter] = previousPeriod.split('-Q')

                    const currentMonths = [(parseInt(currentQuarter) - 1) * 3 + 1, (parseInt(currentQuarter) - 1) * 3 + 2, (parseInt(currentQuarter) - 1) * 3 + 3]
                    const previousMonths = [(parseInt(previousQuarter) - 1) * 3 + 1, (parseInt(previousQuarter) - 1) * 3 + 2, (parseInt(previousQuarter) - 1) * 3 + 3]

                    currentRevenueQuery = currentRevenueQuery.whereIn('month', currentMonths).where('year', parseInt(currentYear))
                    currentCostQuery = currentCostQuery.whereIn('month', currentMonths).where('year', parseInt(currentYear))
                    previousRevenueQuery = previousRevenueQuery.whereIn('month', previousMonths).where('year', parseInt(previousYear))
                    previousCostQuery = previousCostQuery.whereIn('month', previousMonths).where('year', parseInt(previousYear))
                } else {
                    currentRevenueQuery = currentRevenueQuery.where('year', parseInt(currentPeriod))
                    currentCostQuery = currentCostQuery.where('year', parseInt(currentPeriod))
                    previousRevenueQuery = previousRevenueQuery.where('year', parseInt(previousPeriod))
                    previousCostQuery = previousCostQuery.where('year', parseInt(previousPeriod))
                }

                const currentRevenueResult = await currentRevenueQuery.first()
                const currentCostResult = await currentCostQuery.first()
                const previousRevenueResult = await previousRevenueQuery.first()

                const currentRevenue = Number(currentRevenueResult?.total_revenue || 0)
                const currentCost = Number(currentCostResult?.total_cost || 0)
                const previousRevenue = Number(previousRevenueResult?.total_revenue || 0)

                const currentProfit = currentRevenue - currentCost
                const currentROI = currentCost > 0 ? (currentProfit / currentCost * 100) : 0

                // Tính % thay đổi doanh thu
                const revenueChange = previousRevenue > 0
                    ? ((currentRevenue - previousRevenue) / previousRevenue * 100)
                    : 0

                comparisonData.push({
                    name: squad.name,
                    current: currentRevenue,
                    previous: previousRevenue,
                    change: Math.round(revenueChange * 100) / 100,
                    roi: Math.round(currentROI * 100) / 100,
                    profit: currentProfit
                })
            }

            return response.json({
                success: true,
                data: comparisonData
            })
        } catch (error) {
            return response.status(500).json({
                success: false,
                message: 'Lỗi khi lấy dữ liệu so sánh Squad',
                error: error.message
            })
        }
    }

    /**
     * Helper method để tính toán period value
     */
    private calculatePeriod(timeFrame: string, period: string, month: number, quarter: number, year: number) {
        let periodValue: string

        if (timeFrame === 'month') {
            const currentMonth = period === 'current' ? month : (month === 1 ? 12 : month - 1)
            const currentYear = period === 'current' ? year : (month === 1 ? year - 1 : year)
            periodValue = `${currentYear}-${String(currentMonth).padStart(2, '0')}`
        } else if (timeFrame === 'quarter') {
            const currentQuarter = period === 'current' ? quarter : (quarter === 1 ? 4 : quarter - 1)
            const currentYear = period === 'current' ? year : (quarter === 1 ? year - 1 : year)
            periodValue = `${currentYear}-Q${currentQuarter}`
        } else {
            const currentYear = period === 'current' ? year : year - 1
            periodValue = String(currentYear)
        }

        return { periodValue }
    }

    /**
     * Helper method để format status label
     */
    private getStatusLabel(status: string): string {
        const statusMap: Record<string, string> = {
            'planning': 'Đang lên kế hoạch',
            'active': 'Đang phát triển',
            'paused': 'Tạm dừng',
            'completed': 'Hoàn thành',
            'cancelled': 'Đã hủy'
        }
        return statusMap[status] || status
    }
} 