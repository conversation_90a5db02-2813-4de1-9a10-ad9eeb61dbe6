import finance_cost_details from "#models/finance/finance_cost_details"
import finance_currencies_month from "#models/finance/finance_currencies_month"
import finance_squads from "#models/finance/finance_squads"
import helper from "#libraries/vcms/helper"
import finance_revenue from "#models/finance/finance_revenue"
import GoogleSheetLib from "#libraries/vcms/finance/google_sheet_lib"
import axios from "axios"
import finance_cost_types from "#models/finance/finance_cost_types"
import finance_ua_debts from "#models/finance/finance_ua_debts"
import finance_monetization_debts from "#models/finance/finance_monetization_debts"
import finance_bank_accounts from "#models/finance/finance_bank_accounts"
import finance_bank_log from "#models/finance/finance_bank_log"

class data_import {
    public async get_report_mkt(month: number, year: number) {
        try {
            let { data } = await axios.get(`https://api-report.dmobin.studio/month_report?time=${year}-${helper.pad_number(month)}`)
            if (data?.currencies) {
                for (let currency in data?.currencies) {
                    let currency_data = data?.currencies[currency]
                    await finance_currencies_month.updateOrCreate({
                        month: month,
                        year: year,
                        name: currency_data.name
                    }, {
                        month: month,
                        year: year,
                        name: currency_data.name,
                        rate: currency_data.rate
                    })
                }
            }
            if (data?.data_all_month) {
                let _squads = await finance_squads.query().where('status', 1).orderBy('id', 'desc').limit(100)
                let squads: any = new Map()
                if (_squads && _squads.length) {
                    for (let _squad of _squads) {
                        squads.set(_squad.name.toLowerCase(), _squad.id)
                    }
                }
                for (let app_id in data?.data_all_month) {
                    let app_data = data?.data_all_month[app_id]
                    let squad_id = squads.get(app_data.app_info.sub_team.toLowerCase())
                    if (!squad_id) {
                        squad_id = 12
                    }
                    if (app_data?.data?.cost) {
                        await finance_cost_details.query().where('month', month).where('year', year).where('project_id', app_id).where('squad_id', squad_id).delete()
                        for (let network in app_data?.data?.cost) {
                            if (app_data?.data?.cost[network]) {
                                await finance_cost_details.create({
                                    month: month,
                                    year: year,
                                    quarter: Math.ceil(month / 3),
                                    project_id: helper.intval(app_id),
                                    squad_id: squad_id,
                                    expense_type_id: 2,
                                    type: app_data?.app_info?.type || 0,
                                    name: network.charAt(0).toUpperCase() + network.slice(1),
                                    value: app_data?.data?.cost[network]
                                })
                            }
                        }
                    }
                    if (app_data?.data?.revenue) {
                        await finance_revenue.query().where('month', month).where('year', year).where('project_id', app_id).where('squad_id', squad_id).delete()
                        for (let network in app_data?.data?.revenue) {
                            if (app_data?.data?.revenue[network]) {
                                await finance_revenue.create({
                                    month: month,
                                    year: year,
                                    quarter: Math.ceil(month / 3),
                                    project_id: helper.intval(app_id),
                                    squad_id: squad_id,
                                    network_id: network,
                                    name: network.charAt(0).toUpperCase() + network.slice(1),
                                    value: app_data?.data?.revenue[network] || 0,
                                    type: app_data?.app_info?.type || 0
                                })
                            }
                        }
                    }
                }
            }
            console.log("Done get_report_mkt")
        } catch (error) {
            console.log(error)
        }
    }
    public async import_google_sheet() {
        try {
            const spreadsheetId1 = "13wEM1DYaQVy-vJ8WoS4ir8SPtjSe1_wLWNyunxioN1Q";
            const spreadsheetId2 = "1jQbayvRkYtmPejc5zY-RM0daAIzpKPESZmNaY4pLf-g";

            const allData: any = await GoogleSheetLib.readAllSheets(spreadsheetId1);

            let _squads = await finance_squads.query().where('status', 1).orderBy('id', 'desc').limit(100)
            let squads: any = new Map()
            if (_squads && _squads.length) {
                for (let _squad of _squads) {
                    squads.set(_squad.name.toLowerCase(), _squad.id)
                }
            }
            let _currencies_month = await finance_currencies_month.query()
            let currencies_month: any = new Map()
            if (_currencies_month && _currencies_month.length) {
                for (let _currency of _currencies_month) {
                    currencies_month.set(_currency.month + '-' + _currency.year + '-' + _currency.name, _currency.rate)
                }
            }
            let cost_types = await finance_cost_types.query()
            var data_import: any = {}
            if (allData && allData?.['Import_Sổ quỹ CP cố định']) {
                for (let item of allData['Import_Sổ quỹ CP cố định']) {
                    let data_month = item[0].split('-')
                    if (typeof data_month[0] != "undefined" && typeof data_month[1] != "undefined" && helper.intval(data_month[0]) && helper.intval(data_month[1])) {
                        let year = helper.intval(data_month[0])
                        let month = helper.intval(data_month[1])
                        if (typeof data_import[year + '-' + month] == "undefined") {
                            data_import[year + '-' + month] = []
                        }
                        let squad_name = item[7].toLowerCase().trim()
                        let squad_id = squads.get(squad_name)
                        if (squad_id) {
                            let cost_vnd = 0
                            if (typeof item[3] != "undefined") {
                                cost_vnd = helper.floatval(item[3].replace(/,/g, ''))
                            }
                            let rate_vnd = currencies_month.get(month + '-' + year + '-' + 'VND')
                            if (cost_vnd * rate_vnd) {
                                let expense_type_id = 7
                                let name_cost = "Chi phí khác"
                                for (let cost_type of cost_types) {
                                    if (typeof item[9] != "undefined" && item[9].toLowerCase().includes(cost_type.name.toLowerCase())) {
                                        expense_type_id = cost_type.id
                                        break
                                    }
                                }
                                data_import[year + '-' + month].push({
                                    month: month,
                                    quarter: Math.ceil(month / 3),
                                    year: year,
                                    project_id: null,
                                    squad_id: squad_id,
                                    expense_type_id: expense_type_id,
                                    type: 0,
                                    name: typeof item[2] != "undefined" ? item[2] : name_cost,
                                    value: cost_vnd * rate_vnd
                                })
                            }
                        }
                    }
                }
            }
            if (allData && allData?.['Sub_team']) {
                for (let item of allData['Sub_team']) {
                    let data_month = item[0].split('-')
                    if (typeof data_month[0] != "undefined" && typeof data_month[1] != "undefined" && helper.intval(data_month[0]) && helper.intval(data_month[1])) {
                        let year = helper.intval(data_month[0])
                        let month = helper.intval(data_month[1])
                        if (typeof data_import[year + '-' + month] == "undefined") {
                            data_import[year + '-' + month] = []
                        }
                        let squad_name = item[1].toLowerCase().trim()
                        let squad_id = squads.get(squad_name)
                        if (squad_id) {
                            let cost_vnd = 0
                            if (typeof item[5] != "undefined") {
                                cost_vnd = helper.floatval(item[5].replace(/,/g, ''))
                            }
                            let rate_vnd = currencies_month.get(month + '-' + year + '-' + 'VND')
                            if (cost_vnd * rate_vnd) {
                                let expense_type_id = 1
                                let name_cost = "Lương nhân viên"
                                data_import[year + '-' + month].push({
                                    month: month,
                                    quarter: Math.ceil(month / 3),
                                    year: year,
                                    project_id: null,
                                    squad_id: squad_id,
                                    expense_type_id: expense_type_id,
                                    type: 0,
                                    name: name_cost,
                                    value: cost_vnd * rate_vnd
                                })
                            }
                        }
                    }
                }
            }
            for (let month in data_import) {
                let data_month = month.split('-')
                await finance_cost_details.query().where('month', data_month[1]).where('year', data_month[0]).whereNull('project_id').delete()
                await finance_cost_details.createMany(data_import[month])
            }

            const allData2: any = await GoogleSheetLib.readAllSheets(spreadsheetId2);
            if (allData2 && allData2?.['finance_cost']) {
                var data_import_ua_debts: any = {}
                for (let item of allData2['finance_cost']) {
                    let data_month = item[0].split('-')
                    if (typeof data_month[0] != "undefined" && typeof data_month[1] != "undefined" && helper.intval(data_month[0]) && helper.intval(data_month[1])) {
                        let year = helper.intval(data_month[0])
                        let month = helper.intval(data_month[1])
                        if (typeof data_import_ua_debts[year + '-' + month] == "undefined") {
                            data_import_ua_debts[year + '-' + month] = []
                        }
                        let status = -2
                        switch (item[13]) {
                            case "Done":
                                status = 1
                                break
                            case "Pending":
                                status = -1
                                break
                            case "Stop":
                                status = 0
                                break
                            case "Prepaid":
                                status = 2
                                break
                        }
                        let payment_date = null
                        if (typeof item[14] != "undefined" && item[14]) {
                            payment_date = new Date(item[14] + " 23:59:59").getTime() / 1000
                        }
                        let due_date = null
                        if (typeof item[15] != "undefined" && item[15]) {
                            const dateParts = item[15].split('-')
                            if (dateParts.length >= 2) {
                                const year = parseInt(dateParts[0])
                                const month = parseInt(dateParts[1])
                                const lastDayOfMonth = new Date(year, month, 0)
                                lastDayOfMonth.setHours(23, 59, 59, 999)
                                due_date = lastDayOfMonth.getTime() / 1000
                            }
                        }

                        data_import_ua_debts[year + '-' + month].push({
                            month: month,
                            quarter: Math.ceil(month / 3),
                            year: year,
                            account: typeof item[1] != "undefined" ? item[1] : "",
                            network: typeof item[2] != "undefined" ? item[2] : "",
                            team: typeof item[3] != "undefined" ? item[3] : "",
                            network_code: typeof item[4] != "undefined" ? item[4] : "",
                            value: typeof item[7] != "undefined" ? helper.floatval(item[7].replace(/,/g, '')) : 0,
                            currency: "USD",
                            fee_bank: 0,
                            content_transfer: typeof item[9] != "undefined" ? item[9] : "",
                            swift_code: typeof item[10] != "undefined" ? item[10] : "",
                            payment_number: typeof item[11] != "undefined" ? item[11] : "",
                            payment_name: typeof item[12] != "undefined" ? item[12] : "",
                            status: status,
                            payment_date: payment_date,
                            due_date: due_date,
                            note: typeof item[16] != "undefined" ? item[16] : "",
                        })
                    }
                }
                for (let month in data_import_ua_debts) {
                    let data_month = month.split('-')
                    await finance_ua_debts.query().where('month', data_month[1]).where('year', data_month[0]).delete()
                    await finance_ua_debts.createMany(data_import_ua_debts[month])
                }
            }
            if (allData2 && allData2?.['finance_revenue']) {
                var data_import_mo_debts: any = {}
                for (let item of allData2['finance_revenue']) {
                    let data_month = item[0].split('-')
                    if (typeof data_month[0] != "undefined" && typeof data_month[1] != "undefined" && helper.intval(data_month[0]) && helper.intval(data_month[1])) {
                        let year = helper.intval(data_month[0])
                        let month = helper.intval(data_month[1])
                        if (typeof data_import_mo_debts[year + '-' + month] == "undefined") {
                            data_import_mo_debts[year + '-' + month] = []
                        }
                        let expected_date = null
                        if (typeof item[7] != "undefined" && item[7] && item[7].trim() != "-") {
                            expected_date = new Date(item[7] + " 23:59:59").getTime() / 1000
                        }
                        let actual_date = null
                        if (typeof item[8] != "undefined" && item[8] && item[8].trim() != "-") {
                            actual_date = new Date(item[8] + " 23:59:59").getTime() / 1000
                        }
                        let amount = 0
                        if (typeof item[6] != "undefined") {
                            amount = helper.floatval(item[6].replace(/,/g, ''))
                        }
                        let amount_real = 0
                        if (typeof item[9] != "undefined") {
                            amount_real = helper.floatval(item[9].replace(/,/g, ''))
                        }
                        let fee_bank = 0
                        if (typeof item[10] != "undefined") {
                            fee_bank = helper.floatval(item[10].replace(/,/g, ''))
                        }
                        data_import_mo_debts[year + '-' + month].push({
                            month: month,
                            year: year,
                            quarter: Math.ceil(month / 3),
                            team: typeof item[1] != "undefined" ? item[1] : "",
                            network: typeof item[2] != "undefined" ? item[2] : "",
                            network_receiver: typeof item[3] != "undefined" ? item[3] : "",
                            network_code: typeof item[4] != "undefined" ? item[4] : "",
                            day_payment: typeof item[5] != "undefined" ? helper.intval(item[5]) : 0,
                            amount: amount,
                            amount_real: amount_real,
                            expected_date: expected_date,
                            actual_date: actual_date,
                            fee_bank: fee_bank,
                            bank_receiver: typeof item[11] != "undefined" ? item[11] : "",
                            note: typeof item[12] != "undefined" ? item[12] : "",
                            status: amount_real ? 1 : 0,
                        })
                    }
                }
                for (let month in data_import_mo_debts) {
                    let data_month = month.split('-')
                    await finance_monetization_debts.query().where('month', data_month[1]).where('year', data_month[0]).delete()
                    await finance_monetization_debts.createMany(data_import_mo_debts[month])
                }
            }
            if (allData2 && allData2?.['finance_bank_balance']) {
                var data_import_bank_balance: any = []
                for (let item of allData2['finance_bank_balance']) {
                    let bank = await finance_bank_accounts.query().where('account_name', item[3]).first()
                    if (bank?.id && item[0]) {
                        data_import_bank_balance.push({
                            bank_id: bank.id,
                            amount: helper.floatval(item[5].replace(/,/g, '')),
                            date_update: item[0]
                        })
                        bank.current_balance = helper.floatval(item[5].replace(/,/g, ''))
                        bank.last_balance_update = new Date(item[0]).getTime() / 1000
                        await bank.save()
                    }
                }
                if (data_import_bank_balance && data_import_bank_balance.length) {
                    await finance_bank_log.query().delete()
                    await finance_bank_log.createMany(data_import_bank_balance)
                }
            }
        } catch (error) {
            console.log(error)
        }
    }
}
export default new data_import()
