import { BaseModel, beforeSave, column } from '@adonisjs/lucid/orm'
import ColumnData from '#models/core/types/column_data'
import Helper from '#libraries/vcms/helper'

export default class CronHistory extends BaseModel {
    public static table = 'cron_histories'
    public static columns: null | Record<string, ColumnData> = {
        id: {
            type: 'int',
            length: 11,
            required: true,
            column: 12,
            listing: true,
        },
        cron_id: {
            type: 'int',
            length: 11,
            required: true,
            column: 12,
            listing: true,
        },
        process_time: {
            type: 'float',
            column: 12,
            listing: true,
        },
        is_running: {
            type: 'int',
            length: 11,
            column: 12,
            listing: true,
        },
        http_status: {
            type: 'varchar',
            length: 255,
            column: 12,
            listing: true,
        },
        success: {
            type: 'int',
            length: 11,
            column: 12,
            listing: true,
        },
        error: {
            type: 'int',
            length: 11,
            column: 12,
            listing: true,
        },
        total: {
            type: 'int',
            length: 11,
            column: 12,
            listing: true,
        },
        note: {
            type: 'varchar',
            length: 255,
            column: 12,
            listing: true,
        },
        logs: {
            type: 'text',
            length: 65535,
            column: 12,
            listing: true,
        },
        status: {
            type: 'int',
            length: 11,
            column: 12,
            listing: {
                type: 'status',
                enum: {
                    0: 'Inactived',
                    1: 'Actived',
                },
            },
        },
        created: {
            type: 'int',
            length: 11,
            column: 12,
            listing: {
                class: 'text-end',
                type: 'date',
                sortable: true,
            },
            invisible: true,
        },
        updated: {
            type: 'int',
            length: 11,
            column: 12,
            invisible: true,
        },
    }

    @column({ isPrimary: true })
    declare id: number

    @column()
    declare cron_id: number

    @column()
    declare process_time: number

    @column()
    declare is_running: number

    @column()
    declare http_status: string

    @column()
    declare note: string

    @column()
    declare logs: string

    @column()
    declare success: number

    @column()
    declare error: number

    @column()
    declare total: number

    @column()
    declare status: number

    @column()
    declare created: number

    @column()
    declare updated: number

    @beforeSave()
    public static async autoTimestamp(data: CronHistory) {
        if (!data.created) {
            data.created = Helper.now_timestamp()
        }
        data.updated = Helper.now_timestamp()
    }
}
